<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Medical Facility Views -->
    <record id="view_medical_facility_form" model="ir.ui.view">
        <field name="name">medical.facility.form</field>
        <field name="model">medical.facility</field>
        <field name="arch" type="xml">
            <form string="Medical Facility">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_departments" type="object" class="oe_stat_button" icon="fa-sitemap">
                            <field name="department_count" widget="statinfo" string="Departments"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="facility_type"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                        <group>
                            <field name="phone"/>
                            <field name="email"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Address">
                            <field name="address"/>
                        </page>
                        <page string="Departments">
                            <field name="department_ids">
                                <tree editable="bottom">
                                    <field name="code"/>
                                    <field name="name"/>
                                    <field name="description"/>
                                    <field name="active"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_medical_facility_tree" model="ir.ui.view">
        <field name="name">medical.facility.tree</field>
        <field name="model">medical.facility</field>
        <field name="arch" type="xml">
            <tree string="Medical Facilities">
                <field name="code"/>
                <field name="name"/>
                <field name="facility_type"/>
                <field name="phone"/>
                <field name="department_count"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="view_medical_facility_search" model="ir.ui.view">
        <field name="name">medical.facility.search</field>
        <field name="model">medical.facility</field>
        <field name="arch" type="xml">
            <search string="Medical Facilities">
                <field name="name"/>
                <field name="code"/>
                <field name="facility_type"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Facility Type" name="facility_type" domain="[]" context="{'group_by': 'facility_type'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_medical_facility" model="ir.actions.act_window">
        <field name="name">Medical Facilities</field>
        <field name="res_model">medical.facility</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new medical facility
            </p>
            <p>
                Create medical facilities like hospitals, clinics, etc.
            </p>
        </field>
    </record>

    <!-- Medical Department Views -->
    <record id="view_medical_department_form" model="ir.ui.view">
        <field name="name">medical.department.form</field>
        <field name="model">medical.department</field>
        <field name="arch" type="xml">
            <form string="Medical Department">
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="code"/>
                            <field name="facility_id"/>
                            <field name="facility_type"/>
                        </group>
                        <group>
                            <field name="active"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_medical_department_tree" model="ir.ui.view">
        <field name="name">medical.department.tree</field>
        <field name="model">medical.department</field>
        <field name="arch" type="xml">
            <tree string="Medical Departments">
                <field name="code"/>
                <field name="name"/>
                <field name="facility_id"/>
                <field name="facility_type"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="view_medical_department_search" model="ir.ui.view">
        <field name="name">medical.department.search</field>
        <field name="model">medical.department</field>
        <field name="arch" type="xml">
            <search string="Medical Departments">
                <field name="name"/>
                <field name="code"/>
                <field name="facility_id"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Facility" name="facility" domain="[]" context="{'group_by': 'facility_id'}"/>
                    <filter string="Facility Type" name="facility_type" domain="[]" context="{'group_by': 'facility_type'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_medical_department" model="ir.actions.act_window">
        <field name="name">Medical Departments</field>
        <field name="res_model">medical.department</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new medical department
            </p>
            <p>
                Create medical departments for facilities.
            </p>
        </field>
    </record>
</odoo>
