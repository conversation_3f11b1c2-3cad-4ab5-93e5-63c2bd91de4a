# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class MedicalFacility(models.Model):
    _name = 'medical.facility'
    _description = 'Medical Facility'
    _order = 'name'

    name = fields.Char(string='Facility Name', required=True, translate=True)
    code = fields.Char(string='Code')
    facility_type = fields.Selection([
        ('hospital', 'Hospital'),
        ('clinic', 'Clinic'),
        ('pharmacy', 'Pharmacy'),
        ('laboratory', 'Laboratory'),
        ('other', 'Other')
    ], string='Facility Type', required=True, default='hospital')
    
    address = fields.Text(string='Address')
    phone = fields.Char(string='Phone')
    email = fields.Char(string='Email')
    
    active = fields.Boolean(default=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    
    department_ids = fields.One2many('medical.department', 'facility_id', string='Departments')
    department_count = fields.Integer(compute='_compute_department_count', string='Department Count')
    
    @api.depends('department_ids')
    def _compute_department_count(self):
        for facility in self:
            facility.department_count = len(facility.department_ids)
    
    def action_view_departments(self):
        self.ensure_one()
        return {
            'name': _('Departments'),
            'type': 'ir.actions.act_window',
            'res_model': 'medical.department',
            'view_mode': 'tree,form',
            'domain': [('facility_id', '=', self.id)],
            'context': {'default_facility_id': self.id}
        }


class MedicalDepartment(models.Model):
    _name = 'medical.department'
    _description = 'Medical Department'
    _order = 'name'

    name = fields.Char(string='Department Name', required=True, translate=True)
    code = fields.Char(string='Code')
    facility_id = fields.Many2one('medical.facility', string='Medical Facility', required=True, ondelete='cascade')
    facility_type = fields.Selection(related='facility_id.facility_type', string='Facility Type', store=True, readonly=True)
    
    description = fields.Text(string='Description')
    active = fields.Boolean(default=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
