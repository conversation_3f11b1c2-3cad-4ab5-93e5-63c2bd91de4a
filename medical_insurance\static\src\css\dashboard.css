.o_medical_dashboard .card {
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: transform 0.3s ease;
    overflow: hidden;
    border: none;
}

.o_medical_dashboard .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

/* Employee profile card */
.o_medical_dashboard .oe_avatar.rounded-circle {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border: 5px solid #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
    display: block;
}

.o_medical_dashboard .card-header {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    padding: 15px;
}

.o_medical_dashboard .card-body {
    padding: 20px;
}

.o_medical_dashboard .card-body.p-0 {
    padding: 0 !important;
}

.o_medical_dashboard .text-center {
    text-align: center;
}

.o_medical_dashboard h1,
.o_medical_dashboard h2,
.o_medical_dashboard h3,
.o_medical_dashboard h4,
.o_medical_dashboard h5 {
    font-weight: 600;
}

/* Employee info styling */
.o_medical_dashboard .font-weight-bold {
    font-weight: 600 !important;
}

.o_medical_dashboard .row.mt-2 {
    margin-top: 0.75rem !important;
}

/* Tree view styling */
.o_medical_dashboard .o_list_view {
    border: none !important;
}

.o_medical_dashboard .o_list_view thead {
    background-color: #f8f9fc;
}

.o_medical_dashboard .o_list_view thead th {
    border-bottom: 2px solid #e3e6f0;
}

.o_medical_dashboard .o_list_view tbody tr:hover {
    background-color: #f8f9fc;
}

.o_medical_dashboard .btn {
    border-radius: 5px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.o_medical_dashboard .btn:hover {
    transform: scale(1.05);
}

/* Card colors */
.o_medical_dashboard .bg-primary {
    background-color: #4e73df !important;
    background-image: linear-gradient(180deg, #4e73df 10%, #3a54b4 100%);
}

.o_medical_dashboard .bg-success {
    background-color: #1cc88a !important;
    background-image: linear-gradient(180deg, #1cc88a 10%, #13855c 100%);
}

.o_medical_dashboard .bg-info {
    background-color: #36b9cc !important;
    background-image: linear-gradient(180deg, #36b9cc 10%, #258391 100%);
}

.o_medical_dashboard .bg-warning {
    background-color: #f6c23e !important;
    background-image: linear-gradient(180deg, #f6c23e 10%, #dda20a 100%);
}

.o_medical_dashboard .bg-danger {
    background-color: #e74a3b !important;
    background-image: linear-gradient(180deg, #e74a3b 10%, #be2617 100%);
}

/* Statistics cards */
.o_medical_dashboard .card-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.o_medical_dashboard .card-text {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.1rem;
}

/* RTL Support */
.o_medical_dashboard[dir="rtl"] .ml8 {
    margin-right: 8px !important;
    margin-left: 0 !important;
}

/* Chart styles */
.o_medical_dashboard .o_pie_chart,
.o_medical_dashboard .o_bar_chart {
    height: 300px;
    width: 100%;
    position: relative;
}

.o_medical_dashboard .o_pie_chart canvas,
.o_medical_dashboard .o_bar_chart canvas {
    width: 100% !important;
    height: 100% !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .o_medical_dashboard .col-md-3,
    .o_medical_dashboard .col-md-6 {
        margin-bottom: 15px;
    }

    .o_medical_dashboard .o_pie_chart,
    .o_medical_dashboard .o_bar_chart {
        height: 250px;
    }
}
