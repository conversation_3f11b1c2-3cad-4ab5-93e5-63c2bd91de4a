# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة الوصول إلى العمر والحالة الاجتماعية بدون صلاحيات
"""

def test_public_access_fields():
    """
    اختبار الحقول العامة للعمر والحالة الاجتماعية
    """
    print("=== اختبار إصلاح مشكلة الوصول العام للمعلومات الشخصية ===")
    
    # التحقق من وجود env
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        print("استخدم: ./odoo-bin shell -d your_database")
        return
    
    # البحث عن موظف
    employee = env['hr.employee'].search([], limit=1)
    if not employee:
        print("لا يوجد موظفين في النظام")
        return
    
    print(f"الموظف المختار: {employee.name}")
    
    # اختبار الحقول الأصلية
    print("\n=== الحقول الأصلية ===")
    try:
        print(f"تاريخ الميلاد: {employee.birthday}")
        print(f"الحالة الاجتماعية: {employee.marital}")
    except Exception as e:
        print(f"خطأ في الوصول للحقول الأصلية: {e}")
    
    # اختبار الحقول العامة الجديدة
    print("\n=== الحقول العامة الجديدة ===")
    try:
        print(f"العمر (عام): {employee.public_age}")
        print(f"الحالة الاجتماعية (عامة): {employee.public_marital_status}")
    except Exception as e:
        print(f"خطأ في الوصول للحقول العامة: {e}")
    
    # اختبار لوحة التحكم
    print("\n=== اختبار لوحة التحكم ===")
    try:
        dashboard = env['employee.dashboard'].create({'employee_id': employee.id})
        dashboard._compute_employee_public_age()
        dashboard._compute_employee_public_marital()
        
        print(f"العمر في لوحة التحكم: {dashboard.employee_public_age}")
        print(f"الحالة الاجتماعية في لوحة التحكم: {dashboard.employee_public_marital}")
        
        # حذف لوحة التحكم المؤقتة
        dashboard.unlink()
        
    except Exception as e:
        print(f"خطأ في اختبار لوحة التحكم: {e}")
    
    print("\n=== انتهى الاختبار ===")


def test_user_without_hr_permissions():
    """
    اختبار المستخدم بدون صلاحيات HR
    """
    print("\n=== اختبار المستخدم بدون صلاحيات HR ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن مستخدم عادي (بدون صلاحيات HR)
    normal_user = env['res.users'].search([
        ('groups_id', 'not in', [env.ref('hr.group_hr_user').id, env.ref('hr.group_hr_manager').id])
    ], limit=1)
    
    if not normal_user:
        print("لا يوجد مستخدمين عاديين في النظام")
        return
    
    print(f"المستخدم العادي: {normal_user.name}")
    
    # البحث عن موظف مرتبط بهذا المستخدم
    employee = env['hr.employee'].search([('user_id', '=', normal_user.id)], limit=1)
    if not employee:
        print("لا يوجد موظف مرتبط بهذا المستخدم")
        return
    
    print(f"الموظف المرتبط: {employee.name}")
    
    # محاولة الوصول كمستخدم عادي
    try:
        # التبديل للمستخدم العادي
        employee_as_user = employee.sudo(normal_user)
        
        print("\n=== الوصول كمستخدم عادي ===")
        print(f"العمر (عام): {employee_as_user.public_age}")
        print(f"الحالة الاجتماعية (عامة): {employee_as_user.public_marital_status}")
        
        # اختبار لوحة التحكم كمستخدم عادي
        dashboard = env['employee.dashboard'].sudo(normal_user).create({'employee_id': employee.id})
        dashboard._compute_employee_public_age()
        dashboard._compute_employee_public_marital()
        
        print(f"العمر في لوحة التحكم: {dashboard.employee_public_age}")
        print(f"الحالة الاجتماعية في لوحة التحكم: {dashboard.employee_public_marital}")
        
        dashboard.unlink()
        
        print("✅ نجح الوصول للمعلومات الشخصية بدون صلاحيات HR!")
        
    except Exception as e:
        print(f"❌ خطأ في الوصول كمستخدم عادي: {e}")


def test_medical_insurance_dashboard():
    """
    اختبار لوحة تحكم التأمين الطبي
    """
    print("\n=== اختبار لوحة تحكم التأمين الطبي ===")
    
    env = globals().get('env')
    if not env:
        print("خطأ: يجب تشغيل هذا من Odoo shell")
        return
    
    # البحث عن موظف
    employee = env['hr.employee'].search([], limit=1)
    if not employee:
        print("لا يوجد موظفين في النظام")
        return
    
    try:
        # إنشاء لوحة تحكم
        dashboard_id = env['employee.dashboard'].get_dashboard_data()
        if dashboard_id:
            dashboard = env['employee.dashboard'].browse(dashboard_id)
            
            print(f"الموظف: {dashboard.employee_id.name}")
            print(f"العمر: {dashboard.employee_public_age} سنة")
            print(f"الحالة الاجتماعية: {dashboard.employee_public_marital}")
            print(f"الحد الأقصى للتأمين: {dashboard.employee_cap}")
            print(f"الرصيد المتبقي: {dashboard.remaining_balance}")
            
            print("✅ لوحة التحكم تعمل بشكل صحيح!")
            
        else:
            print("❌ فشل في إنشاء لوحة التحكم")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار لوحة التحكم: {e}")


if __name__ == "__main__":
    print("استخدم الدوال التالية:")
    print("1. test_public_access_fields() - لاختبار الحقول العامة")
    print("2. test_user_without_hr_permissions() - لاختبار المستخدم بدون صلاحيات")
    print("3. test_medical_insurance_dashboard() - لاختبار لوحة التحكم")
    print("\nمثال:")
    print("exec(open('/path/to/medical_insurance/test_public_access_fix.py').read())")
    print("test_public_access_fields()")
    print("test_user_without_hr_permissions()")
    print("test_medical_insurance_dashboard()")
