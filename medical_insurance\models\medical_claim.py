# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError, UserError
from datetime import date


class MedicalClaim(models.Model):
    _name = 'medical.claim'
    _description = 'Medical Insurance Claim'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'date desc, id desc'

    name = fields.Char(string='Reference', required=True, copy=False, readonly=True,
                      default=lambda self: _('New'))
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                 tracking=True)
    employee_number = fields.Char(string='Employee Number', readonly=True, store=True)
    date = fields.Date(string='Claim Date', default=fields.Date.today, required=True,
                      tracking=True)

    # Who received treatment
    beneficiary_type = fields.Selection([
        ('employee', 'Employee'),
        ('family_member', 'Family Member')
    ], string='Beneficiary', required=True, default='employee', tracking=True)
    family_member_id = fields.Many2one('medical.family.member', string='Family Member',
                                      domain="[('employee_id', '=', employee_id)]",
                                      tracking=True)

    # Claim details
    claim_type = fields.Selection([
        ('medication', 'Medication'),
        ('consultation', 'Consultation'),
        ('hospitalization', 'Hospitalization'),
        ('other', 'Other')
    ], string='Claim Type', required=True, tracking=True)

    # Medical facility and department
    facility_id = fields.Many2one('medical.facility', string='Medical Facility', tracking=True)
    department_id = fields.Many2one('medical.department', string='Department',
                                   domain="[('facility_id', '=', facility_id)]", tracking=True)

    description = fields.Text(string='Description', tracking=True)

    # Financial details
    company_currency_id = fields.Many2one('res.currency', related='company_id.currency_id',
                                         string='Company Currency', readonly=True)
    total_amount = fields.Float(string='Total Amount', required=True, tracking=True)
    reimbursement_rate = fields.Float(string='Reimbursement Rate (%)', compute='_compute_reimbursement_rate',
                                     store=True)
    reimbursed_amount = fields.Float(string='Reimbursed Amount', compute='_compute_reimbursed_amount',
                                    store=True, tracking=True)

    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('paid', 'Paid')
    ], string='Status', default='draft', tracking=True)

    # Documents
    attachment_ids = fields.Many2many('ir.attachment', string='Attachments')

    # Cap tracking
    employee_cap = fields.Float(string='Employee Cap', compute='_compute_employee_cap', store=True)
    previous_claims_amount = fields.Float(string='Previous Claims Amount', compute='_compute_previous_claims')
    remaining_balance = fields.Float(string='Remaining Balance', compute='_compute_remaining_balance')
    exceeds_cap = fields.Boolean(string='Exceeds Cap', compute='_compute_exceeds_cap')
    
    # Fiscal year tracking
    fiscal_year_id = fields.Many2one('medical.insurance.fiscal.year', string='Fiscal Year', 
                                    compute='_compute_fiscal_year', store=True)
    employee_balance_id = fields.Many2one('medical.insurance.employee.balance', string='Employee Balance',
                                         compute='_compute_employee_balance', store=True)

    @api.model
    def create(self, vals):
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('medical.claim') or _('New')

        # Set employee number if employee_id is provided but employee_number is not
        if vals.get('employee_id') and not vals.get('employee_number'):
            employee = self.env['hr.employee'].browse(vals['employee_id'])
            if hasattr(employee, 'int_id') and employee.int_id:
                vals['employee_number'] = employee.int_id
            else:
                vals['employee_number'] = f"EMP{employee.id:05d}" if employee.id else ""

        return super(MedicalClaim, self).create(vals)

    def write(self, vals):
        # Update employee number if employee_id is changed
        if vals.get('employee_id'):
            employee = self.env['hr.employee'].browse(vals['employee_id'])
            if hasattr(employee, 'int_id') and employee.int_id:
                vals['employee_number'] = employee.int_id
            else:
                vals['employee_number'] = f"EMP{employee.id:05d}" if employee.id else ""

        return super(MedicalClaim, self).write(vals)

    @api.depends('date')
    def _compute_fiscal_year(self):
        """Determine the fiscal year based on the claim date"""
        for claim in self:
            if not claim.date:
                claim.fiscal_year_id = False
                continue
                
            # First try to find an exact match for the fiscal year
            fiscal_year = self.env['medical.insurance.fiscal.year'].search([
                ('start_date', '<=', claim.date),
                ('end_date', '>=', claim.date),
                ('state', 'in', ['open', 'closed'])
            ], limit=1)
            
            # If no exact match, try to find the closest fiscal year
            if not fiscal_year:
                # Try to find the most recent fiscal year that ends before the claim date
                past_fiscal_year = self.env['medical.insurance.fiscal.year'].search([
                    ('end_date', '<', claim.date),
                    ('state', 'in', ['closed', 'archived'])
                ], limit=1, order='end_date desc')
                
                # Try to find the earliest fiscal year that starts after the claim date
                future_fiscal_year = self.env['medical.insurance.fiscal.year'].search([
                    ('start_date', '>', claim.date),
                    ('state', 'in', ['open', 'draft'])
                ], limit=1, order='start_date asc')
                
                # Choose the closest fiscal year
                if past_fiscal_year and future_fiscal_year:
                    # Calculate which one is closer
                    days_to_past = (claim.date - past_fiscal_year.end_date).days
                    days_to_future = (future_fiscal_year.start_date - claim.date).days
                    fiscal_year = past_fiscal_year if days_to_past <= days_to_future else future_fiscal_year
                elif past_fiscal_year:
                    fiscal_year = past_fiscal_year
                elif future_fiscal_year:
                    fiscal_year = future_fiscal_year
            
            claim.fiscal_year_id = fiscal_year.id if fiscal_year else False
    
    @api.depends('employee_id', 'fiscal_year_id')
    def _compute_employee_balance(self):
        """Find the employee balance record for the current fiscal year"""
        for claim in self:
            if not claim.employee_id or not claim.fiscal_year_id:
                claim.employee_balance_id = False
                continue
                
            # Try to find existing balance
            balance = self.env['medical.insurance.employee.balance'].search([
                ('employee_id', '=', claim.employee_id.id),
                ('fiscal_year_id', '=', claim.fiscal_year_id.id)
            ], limit=1)
            
            # If no balance exists and the fiscal year is open or closed, create one
            if not balance and claim.fiscal_year_id.state in ['open', 'closed']:
                # Determine cap based on marital status
                try:
                    if claim.employee_id.marital in ['married', 'married_and_provide']:
                        cap_amount = claim.fiscal_year_id.config_id.married_with_dependents_cap
                    else:
                        cap_amount = claim.fiscal_year_id.config_id.single_cap
                except Exception:
                    # Fallback to higher cap if marital status can't be determined
                    cap_amount = claim.fiscal_year_id.config_id.married_with_dependents_cap
                
                # Create a new balance record
                balance = self.env['medical.insurance.employee.balance'].create({
                    'employee_id': claim.employee_id.id,
                    'fiscal_year_id': claim.fiscal_year_id.id,
                    'initial_balance': cap_amount,
                    'current_balance': cap_amount,
                })
            
            claim.employee_balance_id = balance.id if balance else False
    
    @api.depends('employee_id', 'employee_id.marital', 'employee_balance_id')
    def _compute_employee_cap(self):
        config = self.env['medical.insurance.config'].search([], limit=1)
        for claim in self:
            # If we have an employee balance, use its initial balance as the cap
            if claim.employee_balance_id:
                claim.employee_cap = claim.employee_balance_id.initial_balance
                continue
                
            # Fallback to the old method if no employee balance is found
            try:
                if claim.employee_id.marital == 'single':
                    claim.employee_cap = config.single_cap
                elif claim.employee_id.marital in ['married', 'married_and_provide']:
                    # Treat married employees the same as married with dependents
                    claim.employee_cap = config.married_with_dependents_cap
                else:
                    claim.employee_cap = config.single_cap
            except Exception:
                # في حالة حدوث خطأ في الوصول إلى حقل marital، نستخدم القيمة الافتراضية
                claim.employee_cap = config.married_with_dependents_cap  # استخدم القيمة الأعلى كافتراضي

    @api.depends('claim_type')
    def _compute_reimbursement_rate(self):
        config = self.env['medical.insurance.config'].search([], limit=1)
        for claim in self:
            if claim.claim_type == 'medication':
                claim.reimbursement_rate = config.reimbursement_rate
            else:
                claim.reimbursement_rate = 100.0

    @api.depends('total_amount', 'reimbursement_rate')
    def _compute_reimbursed_amount(self):
        for claim in self:
            claim.reimbursed_amount = claim.total_amount * (claim.reimbursement_rate / 100.0)

    def _compute_previous_claims(self):
        for claim in self:
            if not claim.employee_id:
                claim.previous_claims_amount = 0.0
                continue
                
            # If we have a fiscal year, use it for date filtering
            if claim.fiscal_year_id:
                domain = [
                    ('employee_id', '=', claim.employee_id.id),
                    ('state', 'in', ['approved', 'paid']),
                    ('fiscal_year_id', '=', claim.fiscal_year_id.id),
                ]
                
                # Only exclude current record if it has a valid ID (not a NewId)
                if claim.id and not isinstance(claim.id, str) and claim.id > 0:
                    domain.append(('id', '!=', claim.id))
                
                previous_claims = self.env['medical.claim'].search(domain)
                claim.previous_claims_amount = sum(previous_claims.mapped('reimbursed_amount'))
                
                # If we have an employee balance, make sure the previous claims amount
                # doesn't exceed the initial balance (for consistency)
                if claim.employee_balance_id and claim.previous_claims_amount > claim.employee_balance_id.initial_balance:
                    claim.previous_claims_amount = claim.employee_balance_id.initial_balance - claim.employee_balance_id.current_balance
            else:
                # Fallback to calendar year if no fiscal year is found
                domain = [
                    ('employee_id', '=', claim.employee_id.id),
                    ('state', 'in', ['approved', 'paid']),
                    ('date', '>=', date(date.today().year, 1, 1)),
                    ('date', '<=', date(date.today().year, 12, 31)),
                ]
                
                # Only exclude current record if it has a valid ID (not a NewId)
                if claim.id and not isinstance(claim.id, str) and claim.id > 0:
                    domain.append(('id', '!=', claim.id))
                
                previous_claims = self.env['medical.claim'].search(domain)
                claim.previous_claims_amount = sum(previous_claims.mapped('reimbursed_amount'))

    @api.depends('previous_claims_amount', 'employee_cap')
    def _compute_remaining_balance(self):
        for claim in self:
            claim.remaining_balance = max(0, claim.employee_cap - claim.previous_claims_amount)

    @api.depends('reimbursed_amount', 'remaining_balance')
    def _compute_exceeds_cap(self):
        for claim in self:
            claim.exceeds_cap = claim.reimbursed_amount > claim.remaining_balance

    @api.constrains('beneficiary_type', 'family_member_id')
    def _check_family_member(self):
        for claim in self:
            if claim.beneficiary_type == 'family_member' and not claim.family_member_id:
                raise ValidationError(_('Please select a family member.'))

    @api.constrains('employee_id')
    def _check_employee_marital_status(self):
        for claim in self:
            try:
                if claim.beneficiary_type == 'family_member' and claim.employee_id.marital not in ['married', 'married_and_provide']:
                    raise ValidationError(_('Only employees with status "Married" or "Married with Dependents" can claim for family members.'))
                # Sync family members if needed when creating a claim for a married employee
                if claim.beneficiary_type == 'family_member' and claim.employee_id.marital in ['married', 'married_and_provide'] and not claim.employee_id.family_members_synced:
                    claim.employee_id.sync_family_members()
            except Exception:
                # في حالة حدوث خطأ في الوصول إلى حقل marital، نتجاهل التحقق
                pass

    @api.onchange('employee_id')
    def _onchange_employee_id(self):
        """When employee is selected, sync family members if needed and update cap information"""
        if not self.employee_id:
            return

        try:
            # Sync family members if needed
            if self.employee_id.marital in ['married', 'married_and_provide'] and not self.employee_id.family_members_synced:
                self.employee_id.sync_family_members()
        except Exception:
            # في حالة حدوث خطأ في الوصول إلى حقل marital، نتجاهل المزامنة
            pass

        # Update employee number if int_id field exists
        if hasattr(self.employee_id, 'int_id') and self.employee_id.int_id:
            self.employee_number = self.employee_id.int_id
        else:
            # Fallback to employee name or ID if int_id doesn't exist
            self.employee_number = f"EMP{self.employee_id.id:05d}" if self.employee_id.id else ""

        # Update cap information
        self._compute_employee_cap()
        self._compute_previous_claims()
        self._compute_remaining_balance()

    @api.onchange('beneficiary_type')
    def _onchange_beneficiary_type(self):
        """When beneficiary type is changed to family member, ensure family members are synced"""
        if self.beneficiary_type == 'family_member' and self.employee_id:
            try:
                # Force sync family members for married employees
                if self.employee_id.marital in ['married', 'married_and_provide']:
                    self.employee_id.sync_family_members()
                    # Return a warning if no family members found
                    if not self.employee_id.family_member_ids:
                        return {
                            'warning': {
                                'title': _('No Family Members'),
                                'message': _('No family members found for this employee. Please add family members first.')
                            }
                        }
            except Exception:
                # في حالة حدوث خطأ في الوصول إلى حقل marital، نتجاهل المزامنة
                pass

    @api.onchange('facility_id')
    def _onchange_facility_id(self):
        """When facility is changed, reset department"""
        self.department_id = False

    def action_submit(self):
        for claim in self:
            if claim.exceeds_cap:
                raise UserError(_('This claim exceeds the remaining balance for this employee.'))
            claim.state = 'submitted'

    def action_approve(self):
        for claim in self:
            claim.state = 'approved'

    def action_reject(self):
        for claim in self:
            claim.state = 'rejected'

    def action_pay(self):
        for claim in self:
            # Update employee balance if available
            if claim.employee_balance_id and claim.fiscal_year_id.state == 'open':
                # Calculate new balance
                new_balance = claim.employee_balance_id.current_balance - claim.reimbursed_amount
                if new_balance < 0:
                    new_balance = 0
                
                # Update the balance
                claim.employee_balance_id.current_balance = new_balance
            
            claim.state = 'paid'

    def action_reset_to_draft(self):
        for claim in self:
            claim.state = 'draft'

    def action_refresh_cap_info(self):
        """Refresh cap information"""
        for claim in self:
            claim._compute_employee_cap()
            claim._compute_previous_claims()
            claim._compute_remaining_balance()
            claim._compute_exceeds_cap()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Cap information has been refreshed.'),
                'sticky': False,
                'type': 'success',
            }
        }
