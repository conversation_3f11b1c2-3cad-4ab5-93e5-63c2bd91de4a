# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import date, timedelta
import random
import string
from odoo.exceptions import ValidationError


class MedicalInsuranceCard(models.Model):
    _name = 'medical.insurance.card'
    _description = 'Medical Insurance Card'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'card_number'
    _order = 'issue_date desc'

    @api.model
    def _get_default_expiry_date(self):
        return date.today() + timedelta(days=365)

    @api.model
    def _generate_card_number(self):
        # Generate a random card number with format: MIC-XXXXXXXX
        letters = string.ascii_uppercase + string.digits
        random_string = ''.join(random.choice(letters) for _ in range(8))
        return f"MIC-{random_string}"

    card_number = fields.Char(string='Card Number', default=_generate_card_number,
                             required=True, readonly=True, copy=False, tracking=True)
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                 tracking=True, ondelete='cascade')
    employee_image = fields.Binary(related='employee_id.image_1920', string='Employee Image', readonly=True)
    employee_number = fields.Char(string='Employee ID', compute='_compute_employee_number', store=True)
    employee_name = fields.Char(related='employee_id.name', string='Employee Name', readonly=True)
    employee_job = fields.Char(string='Job Title', compute='_compute_employee_job', store=True)
    employee_department = fields.Many2one(related='employee_id.department_id', string='Department', readonly=True)

    issue_date = fields.Date(string='Issue Date', default=fields.Date.today, required=True, tracking=True)
    expiry_date = fields.Date(string='Expiry Date', default=_get_default_expiry_date, required=True, tracking=True)

    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', tracking=True)

    active = fields.Boolean(default=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    family_card_ids = fields.One2many('medical.insurance.family.card', 'card_id', string='Family Member Cards')
    family_card_count = fields.Integer(compute='_compute_family_card_count', string='Family Card Count')

    notes = fields.Text(string='Notes')

    @api.depends('family_card_ids')
    def _compute_family_card_count(self):
        for card in self:
            card.family_card_count = len(card.family_card_ids)

    @api.depends('employee_id')
    def _compute_employee_number(self):
        for card in self:
            # استخدام رقم الموظف من حقل barcode إذا كان متوفراً، وإلا استخدام معرف الموظف
            if card.employee_id:
                if hasattr(card.employee_id, 'int_id') and card.employee_id.int_id:
                    card.employee_number = card.employee_id.int_id
                elif hasattr(card.employee_id, 'barcode') and card.employee_id.barcode:
                    card.employee_number = card.employee_id.barcode
                else:
                    card.employee_number = str(card.employee_id.id)

    @api.depends('employee_id')
    def _compute_employee_job(self):
        for card in self:
            if card.employee_id:
                if hasattr(card.employee_id, 'job_title') and card.employee_id.job_title:
                    card.employee_job = card.employee_id.job_title
                elif hasattr(card.employee_id, 'job_id') and card.employee_id.job_id:
                    card.employee_job = card.employee_id.job_id.name
                else:
                    card.employee_job = ''
            else:
                card.employee_job = ''

    @api.model
    def create(self, vals):
        record = super(MedicalInsuranceCard, self).create(vals)
        # Automatically create cards for family members if employee is married or has dependents
        if (record.employee_id.marital in ['married', 'married_and_provide']) and record.employee_id.family_member_ids:
            self._create_family_cards(record)
        return record

    def _create_family_cards(self, card):
        """Create insurance cards for family members"""
        family_card_obj = self.env['medical.insurance.family.card']
        for family_member in card.employee_id.family_member_ids:
            family_card_obj.create({
                'card_id': card.id,
                'family_member_id': family_member.id,
                'issue_date': card.issue_date,
                'expiry_date': card.expiry_date,
            })

    def action_activate(self):
        for card in self:
            card.state = 'active'

    def action_cancel(self):
        for card in self:
            card.state = 'cancelled'

    def action_draft(self):
        for card in self:
            card.state = 'draft'

    @api.model
    def _cron_check_expired_cards(self):
        """Cron job to check and update expired cards"""
        today = fields.Date.today()
        expired_cards = self.search([
            ('expiry_date', '<', today),
            ('state', '=', 'active')
        ])
        expired_cards.write({'state': 'expired'})

    def action_renew_card(self):
        """Renew the card for another year"""
        for card in self:
            if card.state in ['active', 'expired']:
                new_expiry = fields.Date.today() + timedelta(days=365)
                card.write({
                    'issue_date': fields.Date.today(),
                    'expiry_date': new_expiry,
                    'state': 'active'
                })
                # Also renew family cards
                for family_card in card.family_card_ids:
                    family_card.write({
                        'issue_date': fields.Date.today(),
                        'expiry_date': new_expiry,
                        'state': 'active'
                    })

    def action_print_card(self):
        """Print the insurance card"""
        self.ensure_one()
        return self.env.ref('medical_insurance.action_report_medical_insurance_card').report_action(self)

    def action_view_family_cards(self):
        """View family member cards"""
        self.ensure_one()
        return {
            'name': _('Family Member Cards'),
            'view_mode': 'tree,form',
            'res_model': 'medical.insurance.family.card',
            'domain': [('card_id', '=', self.id)],
            'type': 'ir.actions.act_window',
            'context': {'default_card_id': self.id}
        }


class MedicalInsuranceFamilyCard(models.Model):
    _name = 'medical.insurance.family.card'
    _description = 'Medical Insurance Family Member Card'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _rec_name = 'card_number'

    @api.model
    def _generate_card_number(self):
        # Generate a random card number with format: MICF-XXXXXXXX
        letters = string.ascii_uppercase + string.digits
        random_string = ''.join(random.choice(letters) for _ in range(8))
        return f"MICF-{random_string}"

    card_number = fields.Char(string='Card Number', default=_generate_card_number,
                             required=True, readonly=True, copy=False, tracking=True)
    card_id = fields.Many2one('medical.insurance.card', string='Employee Card',
                             required=True, ondelete='cascade', tracking=True)
    family_member_id = fields.Many2one('medical.family.member', string='Family Member',
                                      required=True, ondelete='cascade', tracking=True,
                                      domain="[('employee_id', '=', employee_id)]")
    employee_id = fields.Many2one('hr.employee', related='card_id.employee_id',
                                 string='Employee', store=True, readonly=True)
    member_name = fields.Char(related='family_member_id.name', string='Name', readonly=True)
    relationship = fields.Selection(related='family_member_id.relationship', string='Relationship', readonly=True)
    employee_job = fields.Char(related='card_id.employee_job', string='Employee Job Title', readonly=True)

    issue_date = fields.Date(string='Issue Date', default=fields.Date.today, required=True, tracking=True)
    expiry_date = fields.Date(string='Expiry Date', required=True, tracking=True)

    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled')
    ], string='Status', default='draft', tracking=True)

    active = fields.Boolean(default=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    notes = fields.Text(string='Notes')

    def action_activate(self):
        for card in self:
            card.state = 'active'

    def action_cancel(self):
        for card in self:
            card.state = 'cancelled'

    def action_draft(self):
        for card in self:
            card.state = 'draft'

    def action_print_card(self):
        """Print the family member insurance card"""
        self.ensure_one()
        return self.env.ref('medical_insurance.action_report_medical_insurance_family_card').report_action(self)
