# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import date


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # Override marital field to add 'married_and_provide' option if not already present
    marital = fields.Selection([
        ('single', 'Single'),
        ('married', 'Married'),
        ('married_and_provide', 'Married with Dependents'),
        ('cohabitant', 'Legal Cohabitant'),
        ('widower', 'Widower'),
        ('divorced', 'Divorced')
    ], string='Marital Status', default='single', tracking=True)

    # Add a field to track if family members have been synced
    family_members_synced = fields.Boolean(string='Family Members Synced', default=False)

    # Family members
    family_member_ids = fields.One2many('medical.family.member', 'employee_id', string='Family Members')
    family_member_count = fields.Integer(compute='_compute_family_member_count', string='Family Member Count')

    # Insurance information
    medical_claim_ids = fields.One2many('medical.claim', 'employee_id', string='Medical Claims')
    medical_claim_count = fields.Integer(compute='_compute_medical_claim_count', string='Medical Claim Count')

    # Insurance cards have been removed

    company_currency = fields.Many2one('res.currency', related='company_id.currency_id',
                                      string='Company Currency', readonly=True)
    insurance_cap = fields.Float(string='Insurance Cap', compute='_compute_insurance_cap')
    used_insurance_amount = fields.Float(string='Used Insurance Amount', compute='_compute_used_insurance_amount')
    remaining_insurance_balance = fields.Float(string='Remaining Balance', compute='_compute_remaining_insurance_balance')

    @api.depends('family_member_ids')
    def _compute_family_member_count(self):
        for employee in self:
            employee.family_member_count = len(employee.family_member_ids)

    @api.depends('medical_claim_ids')
    def _compute_medical_claim_count(self):
        for employee in self:
            employee.medical_claim_count = len(employee.medical_claim_ids)

    # Insurance card count computation has been removed

    @api.depends('marital')
    def _compute_insurance_cap(self):
        config = self.env['medical.insurance.config'].search([], limit=1)
        for employee in self:
            try:
                if employee.marital == 'single':
                    employee.insurance_cap = config.single_cap
                elif employee.marital in ['married', 'married_and_provide']:
                    # Treat married employees the same as married with dependents
                    employee.insurance_cap = config.married_with_dependents_cap
                else:
                    employee.insurance_cap = config.single_cap
            except Exception:
                # في حالة حدوث خطأ في الوصول إلى حقل marital، نستخدم القيمة الافتراضية
                employee.insurance_cap = config.married_with_dependents_cap  # استخدم القيمة الأعلى كافتراضي

    def _compute_used_insurance_amount(self):
        for employee in self:
            if not employee.id or isinstance(employee.id, str):
                employee.used_insurance_amount = 0.0
                continue

            domain = [
                ('employee_id', '=', employee.id),
                ('state', 'in', ['approved', 'paid']),
                ('date', '>=', date(date.today().year, 1, 1)),
                ('date', '<=', date(date.today().year, 12, 31))
            ]
            claims = self.env['medical.claim'].search(domain)
            employee.used_insurance_amount = sum(claims.mapped('reimbursed_amount'))

    @api.depends('insurance_cap', 'used_insurance_amount')
    def _compute_remaining_insurance_balance(self):
        for employee in self:
            employee.remaining_insurance_balance = max(0, employee.insurance_cap - employee.used_insurance_amount)

    def action_view_family_members(self):
        self.ensure_one()
        return {
            'name': _('Family Members'),
            'type': 'ir.actions.act_window',
            'res_model': 'medical.family.member',
            'view_mode': 'tree,form',
            'domain': [('employee_id', '=', self.id)],
            'context': {'default_employee_id': self.id}
        }

    def action_view_medical_claims(self):
        self.ensure_one()
        return {
            'name': _('Medical Claims'),
            'type': 'ir.actions.act_window',
            'res_model': 'medical.claim',
            'view_mode': 'tree,form',
            'domain': [('employee_id', '=', self.id)],
            'context': {'default_employee_id': self.id}
        }

    # Insurance card actions have been removed

    @api.model
    def create(self, vals):
        """Override to sync family members if employee is married or has dependents"""
        res = super(HrEmployee, self).create(vals)
        try:
            if res.marital in ['married', 'married_and_provide']:
                res.sync_family_members()
        except Exception:
            # في حالة حدوث خطأ في الوصول إلى حقل marital، نتجاهل المزامنة
            pass
        return res

    def write(self, vals):
        """Override to sync family members if marital status changes to married or married with dependents"""
        res = super(HrEmployee, self).write(vals)
        if 'marital' in vals and vals['marital'] in ['married', 'married_and_provide']:
            try:
                for employee in self:
                    employee.sync_family_members()
            except Exception:
                # في حالة حدوث خطأ في الوصول، نتجاهل المزامنة
                pass
        return res

    def sync_family_members(self):
        """Sync family members from hr.employee.provide to medical.family.member"""
        self.ensure_one()

        # First, remove any generic "Spouse" entries
        generic_spouse = self.env['medical.family.member'].search([
            ('employee_id', '=', self.id),
            ('name', '=', 'Spouse')
        ])
        if generic_spouse:
            generic_spouse.unlink()

        # Get existing family members to avoid duplicates
        existing_family_members = {member.name: member for member in self.family_member_ids}

        try:
            # Check if the employee is married and has spouse_complete_name
            if self.marital == 'married' and hasattr(self, 'spouse_complete_name') and self.spouse_complete_name:
                # Check if spouse already exists in family members
                if self.spouse_complete_name not in existing_family_members:
                    # Create spouse family member
                    self.env['medical.family.member'].create({
                        'name': self.spouse_complete_name,
                        'employee_id': self.id,
                        'birth_date': self.spouse_birthdate if hasattr(self, 'spouse_birthdate') else False,
                        'relationship': 'spouse',
                        'notes': _('Spouse from employee record'),
                    })
                    # Mark as synced
                    self.family_members_synced = True
                    return
        except Exception:
            # في حالة حدوث خطأ في الوصول إلى حقل marital، نتابع التنفيذ
            pass

        # Check if the employee has providers_ids (family members in hr.employee.provide)
        if hasattr(self, 'providers_ids'):
            # Create new family members from providers_ids
            providers_found = False
            for provider in self.providers_ids:
                providers_found = True
                if provider.name not in existing_family_members:
                    # Map relationship - in hr.employee.provide it's a char field, we need to map it to our selection field
                    relationship = 'other'  # Default
                    if provider.relation:
                        relation_lower = provider.relation.lower()
                        if 'زوج' in relation_lower or 'زوجة' in relation_lower or 'زوج' in relation_lower:
                            relationship = 'spouse'
                        elif 'ابن' in relation_lower or 'بنت' in relation_lower or 'طفل' in relation_lower:
                            relationship = 'child'

                    # Create new family member
                    self.env['medical.family.member'].create({
                        'name': provider.name,
                        'employee_id': self.id,
                        'birth_date': provider.dob if hasattr(provider, 'dob') else False,
                        'relationship': relationship,
                        'notes': provider.relation if hasattr(provider, 'relation') else False,
                    })

            # If providers found, mark as synced
            if providers_found:
                self.family_members_synced = True
                return

        try:
            # If no spouse_complete_name and no providers_ids but employee is married,
            # we don't create a generic spouse anymore
            if self.marital == 'married' and not self.family_member_ids:
                # Mark as synced even though we didn't create any family members
                # This prevents the system from trying to sync again
                self.family_members_synced = True
        except Exception:
            # في حالة حدوث خطأ في الوصول إلى حقل marital، نتجاهل هذا الجزء
            pass

    def action_sync_family_members(self):
        """Action to manually sync family members"""
        for employee in self:
            employee.sync_family_members()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Family members synchronized successfully'),
                'sticky': False,
                'type': 'success',
            }
        }

    @api.model
    def cleanup_generic_spouse_records(self):
        """Remove all generic 'Spouse' records from the system"""
        generic_spouses = self.env['medical.family.member'].search([
            ('name', '=', 'Spouse')
        ])
        if generic_spouses:
            generic_spouses.unlink()
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Generic spouse records removed successfully'),
                    'sticky': False,
                    'type': 'success',
                }
            }
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Information'),
                'message': _('No generic spouse records found'),
                'sticky': False,
                'type': 'info',
            }
        }
