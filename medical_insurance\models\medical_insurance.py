# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import ValidationError
from datetime import date


class MedicalInsuranceConfig(models.Model):
    _name = 'medical.insurance.config'
    _description = 'Medical Insurance Configuration'
    _rec_name = 'name'

    name = fields.Char(string='Name', required=True)
    active = fields.Boolean(default=True)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)
    reimbursement_rate = fields.Float(string='Reimbursement Rate (%)', default=80.0,
                                     help='Percentage of medical expenses that will be reimbursed')

    # Spending caps based on marital status
    single_cap = fields.Float(string='Single Employee Cap', required=True,
                             help='Maximum amount covered for single employees')
    married_with_dependents_cap = fields.Float(string='Married with Dependents Cap', required=True,
                                             help='Maximum amount covered for married employees with dependents')
    
    # Financial Year Management
    fiscal_year_ids = fields.One2many('medical.insurance.fiscal.year', 'config_id', string='Fiscal Years')
    current_fiscal_year_id = fields.Many2one('medical.insurance.fiscal.year', string='Current Fiscal Year',
                                            compute='_compute_current_fiscal_year', store=True)
    
    @api.depends('fiscal_year_ids.start_date', 'fiscal_year_ids.end_date', 'fiscal_year_ids.state')
    def _compute_current_fiscal_year(self):
        """Compute the current active fiscal year"""
        today = fields.Date.today()
        for record in self:
            active_fiscal_year = record.fiscal_year_ids.filtered(
                lambda fy: fy.start_date <= today <= fy.end_date and fy.state == 'open'
            )
            record.current_fiscal_year_id = active_fiscal_year[0] if active_fiscal_year else False

    @api.constrains('reimbursement_rate')
    def _check_reimbursement_rate(self):
        for record in self:
            if record.reimbursement_rate <= 0.0 or record.reimbursement_rate > 100.0:
                raise ValidationError(_('Reimbursement rate must be between 0 and 100.'))

    @api.constrains('single_cap', 'married_with_dependents_cap')
    def _check_caps(self):
        for record in self:
            if record.single_cap <= 0.0:
                raise ValidationError(_('Single employee cap must be greater than zero.'))
            if record.married_with_dependents_cap <= 0.0:
                raise ValidationError(_('Married with dependents cap must be greater than zero.'))

    def cleanup_generic_spouse_records(self):
        """Remove all generic 'Spouse' records from the system"""
        self.ensure_one()
        generic_spouses = self.env['medical.family.member'].search([
            ('name', '=', 'Spouse')
        ])
        if generic_spouses:
            generic_spouses.unlink()
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Generic spouse records removed successfully'),
                    'sticky': False,
                    'type': 'success',
                }
            }
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Information'),
                'message': _('No generic spouse records found'),
                'sticky': False,
                'type': 'info',
            }
        }
    
    def action_create_fiscal_year(self):
        """Create a new fiscal year"""
        self.ensure_one()
        return {
            'name': _('Create Fiscal Year'),
            'type': 'ir.actions.act_window',
            'res_model': 'medical.insurance.fiscal.year',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_config_id': self.id,
                'default_name': f"FY {date.today().year}-{date.today().year + 1}",
                'default_start_date': date.today().replace(month=1, day=1),
                'default_end_date': date.today().replace(month=12, day=31),
            }
        }
    
    def action_close_current_fiscal_year(self):
        """Close the current fiscal year and reset employee balances"""
        self.ensure_one()
        if not self.current_fiscal_year_id:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Warning'),
                    'message': _('No active fiscal year found to close.'),
                    'sticky': False,
                    'type': 'warning',
                }
            }
        
        # Check if there's a next fiscal year ready (in draft state)
        next_fiscal_year = self.env['medical.insurance.fiscal.year'].search([
            ('config_id', '=', self.id),
            ('state', '=', 'draft')
        ], limit=1, order='start_date')
        
        if not next_fiscal_year:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Warning'),
                    'message': _('Please create the next fiscal year before closing the current one.'),
                    'sticky': False,
                    'type': 'warning',
                }
            }
        
        # Close current fiscal year
        self.current_fiscal_year_id.action_close()
        
        # Open next fiscal year
        next_fiscal_year.action_open()
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Fiscal year closed successfully. Employee balances have been reset for the new fiscal year.'),
                'sticky': False,
                'type': 'success',
            }
        }


class MedicalInsuranceFiscalYear(models.Model):
    _name = 'medical.insurance.fiscal.year'
    _description = 'Medical Insurance Fiscal Year'
    _order = 'start_date desc'
    
    name = fields.Char(string='Name', required=True)
    config_id = fields.Many2one('medical.insurance.config', string='Insurance Configuration', required=True, ondelete='cascade')
    start_date = fields.Date(string='Start Date', required=True)
    end_date = fields.Date(string='End Date', required=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('open', 'Open'),
        ('closed', 'Closed'),
        ('archived', 'Archived')
    ], string='Status', default='draft', tracking=True)
    
    employee_balance_ids = fields.One2many('medical.insurance.employee.balance', 'fiscal_year_id', string='Employee Balances')
    company_id = fields.Many2one(related='config_id.company_id', string='Company', store=True, readonly=True)
    
    _sql_constraints = [
        ('date_check', 'CHECK(start_date <= end_date)', 'Start date must be before end date!'),
        ('name_company_uniq', 'UNIQUE(name, company_id)', 'Fiscal year name must be unique per company!')
    ]
    
    @api.constrains('start_date', 'end_date', 'config_id')
    def _check_dates(self):
        for record in self:
            # Check for overlapping fiscal years that are in 'open' state
            # We only care about overlapping with open fiscal years
            overlapping = self.search([
                ('id', '!=', record.id),
                ('config_id', '=', record.config_id.id),
                ('state', '=', 'open'),
                ('start_date', '<=', record.end_date),
                ('end_date', '>=', record.start_date)
            ])
            if overlapping:
                raise ValidationError(_('Fiscal year dates overlap with existing open fiscal year: %s') % overlapping[0].name)
    
    def action_open(self):
        """Open the fiscal year and create employee balances"""
        self.ensure_one()
        if self.state != 'draft':
            raise ValidationError(_('Only draft fiscal years can be opened.'))
        
        # Check if there's already an open fiscal year
        open_fiscal_year = self.search([
            ('config_id', '=', self.config_id.id),
            ('state', '=', 'open'),
            ('id', '!=', self.id)
        ])
        if open_fiscal_year:
            raise ValidationError(_('There is already an open fiscal year: %s. Please close it first.') % open_fiscal_year[0].name)
        
        # Create employee balances for all employees
        employees = self.env['hr.employee'].search([('company_id', '=', self.company_id.id)])
        for employee in employees:
            # Check if balance already exists
            existing_balance = self.env['medical.insurance.employee.balance'].search([
                ('employee_id', '=', employee.id),
                ('fiscal_year_id', '=', self.id)
            ], limit=1)
            
            if not existing_balance:
                # Determine cap based on marital status
                try:
                    if employee.marital in ['married', 'married_and_provide']:
                        cap_amount = self.config_id.married_with_dependents_cap
                    else:
                        cap_amount = self.config_id.single_cap
                except Exception:
                    # Fallback to higher cap if marital status can't be determined
                    cap_amount = self.config_id.married_with_dependents_cap
                    
                self.env['medical.insurance.employee.balance'].create({
                    'employee_id': employee.id,
                    'fiscal_year_id': self.id,
                    'initial_balance': cap_amount,
                    'current_balance': cap_amount,
                })
        
        self.state = 'open'
        return True
    
    def action_close(self):
        """Close the fiscal year"""
        self.ensure_one()
        if self.state != 'open':
            raise ValidationError(_('Only open fiscal years can be closed.'))
        
        self.state = 'closed'
        return True
    
    def action_archive(self):
        """Archive the fiscal year"""
        self.ensure_one()
        if self.state != 'closed':
            raise ValidationError(_('Only closed fiscal years can be archived.'))
        
        self.state = 'archived'
        return True
    
    def action_reset_to_draft(self):
        """Reset fiscal year to draft (for administrative purposes)"""
        self.ensure_one()
        if self.state == 'archived':
            raise ValidationError(_('Archived fiscal years cannot be reset to draft.'))
        
        # Delete all employee balances
        self.employee_balance_ids.unlink()
        self.state = 'draft'
        return True


class MedicalInsuranceEmployeeBalance(models.Model):
    _name = 'medical.insurance.employee.balance'
    _description = 'Medical Insurance Employee Balance'
    
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True, ondelete='cascade')
    fiscal_year_id = fields.Many2one('medical.insurance.fiscal.year', string='Fiscal Year', required=True, ondelete='cascade')
    initial_balance = fields.Float(string='Initial Balance', required=True, 
                                  help='Initial insurance balance allocated to the employee for this fiscal year')
    current_balance = fields.Float(string='Current Balance', required=True,
                                  help='Current remaining balance for the employee')
    used_amount = fields.Float(string='Used Amount', compute='_compute_used_amount', store=True,
                              help='Amount used from the insurance balance')
    company_id = fields.Many2one(related='fiscal_year_id.company_id', string='Company', store=True, readonly=True)
    
    _sql_constraints = [
        ('employee_fiscal_year_uniq', 'UNIQUE(employee_id, fiscal_year_id)', 
         'An employee can have only one balance record per fiscal year!')
    ]
    
    @api.depends('initial_balance', 'current_balance')
    def _compute_used_amount(self):
        for record in self:
            record.used_amount = record.initial_balance - record.current_balance


class FamilyMember(models.Model):
    _name = 'medical.family.member'
    _description = 'Family Member'
    _rec_name = 'name'

    name = fields.Char(string='Name', required=True)
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True, ondelete='cascade')
    birth_date = fields.Date(string='Birth Date')
    relationship = fields.Selection([
        ('spouse', 'Spouse'),
        ('child', 'Child'),
        ('other', 'Other')
    ], string='Relationship', required=True)
    notes = fields.Text(string='Notes')
    active = fields.Boolean(default=True)

    # Company is derived from employee
    company_id = fields.Many2one(related='employee_id.company_id', string='Company', store=True, readonly=True)
