# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import date
import logging

_logger = logging.getLogger(__name__)


class EmployeePublicInfo(models.Model):
    """
    نموذج للمعلومات العامة للموظف بدون قيود صلاحيات
    يتم تحديث هذا النموذج تلقائياً عند تغيير بيانات الموظف
    """
    _name = 'employee.public.info'
    _description = 'Employee Public Information'
    _rec_name = 'employee_name'

    employee_id = fields.Many2one('hr.employee', string='Employee', required=True, ondelete='cascade')
    employee_name = fields.Char(string='Employee Name', required=True)
    employee_age = fields.Integer(string='Age', default=0)
    employee_marital_status = fields.Char(string='Marital Status', default='')
    employee_blood_type = fields.Char(string='Blood Type', default='')
    employee_mobile = fields.Char(string='Mobile Phone', default='')
    employee_email = fields.Char(string='Email', default='')
    employee_job_title = fields.Char(string='Job Title', default='')
    employee_department = fields.Char(string='Department', default='')
    employee_number = fields.Char(string='Employee Number', default='')
    
    # حقول للتحديث التلقائي
    last_updated = fields.Datetime(string='Last Updated', default=fields.Datetime.now)
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    _sql_constraints = [
        ('employee_unique', 'UNIQUE(employee_id)', 'Employee public info must be unique per employee!')
    ]

    @api.model
    def create_or_update_employee_info(self, employee_id):
        """
        إنشاء أو تحديث المعلومات العامة للموظف
        """
        if not employee_id:
            return False
            
        employee = self.env['hr.employee'].sudo().browse(employee_id)
        if not employee.exists():
            return False

        # البحث عن سجل موجود
        existing_info = self.search([('employee_id', '=', employee_id)], limit=1)
        
        # تحضير البيانات
        data = self._prepare_employee_data(employee)
        
        if existing_info:
            # تحديث السجل الموجود
            existing_info.write(data)
            return existing_info
        else:
            # إنشاء سجل جديد
            data['employee_id'] = employee_id
            return self.create(data)

    def _prepare_employee_data(self, employee):
        """
        تحضير بيانات الموظف للحفظ
        """
        # حساب العمر
        age = 0
        if employee.birthday:
            today = date.today()
            born = employee.birthday
            age = today.year - born.year - ((today.month, today.day) < (born.month, born.day))

        # تحويل الحالة الاجتماعية
        marital_dict = {
            'single': 'Single',
            'married': 'Married',
            'married_and_provide': 'Married with Dependents',
            'cohabitant': 'Legal Cohabitant',
            'widower': 'Widower',
            'divorced': 'Divorced'
        }
        marital_status = marital_dict.get(employee.marital, employee.marital or '')

        # تحويل فصيلة الدم
        blood_type_map = {
            '1': 'A+', '2': 'A-', '3': 'B+', '4': 'B-',
            '5': 'AB+', '6': 'AB-', '7': 'O+', '8': 'O-'
        }
        blood_type = ''
        if hasattr(employee, 'bloodtype') and employee.bloodtype:
            blood_type = blood_type_map.get(employee.bloodtype, '')

        # رقم الموظف
        employee_number = ''
        if hasattr(employee, 'int_id') and employee.int_id:
            employee_number = employee.int_id
        else:
            employee_number = f"EMP{employee.id:05d}" if employee.id else ""

        return {
            'employee_name': employee.name or '',
            'employee_age': age,
            'employee_marital_status': marital_status,
            'employee_blood_type': blood_type,
            'employee_mobile': employee.mobile_phone or '',
            'employee_email': employee.work_email or '',
            'employee_job_title': employee.job_title or (employee.job_id.name if employee.job_id else ''),
            'employee_department': employee.department_id.name if employee.department_id else '',
            'employee_number': employee_number,
            'last_updated': fields.Datetime.now(),
            'company_id': employee.company_id.id if employee.company_id else self.env.company.id
        }

    @api.model
    def update_all_employees_info(self):
        """
        تحديث معلومات جميع الموظفين
        يمكن استخدامها كـ cron job
        """
        employees = self.env['hr.employee'].sudo().search([])
        updated_count = 0
        
        for employee in employees:
            try:
                self.create_or_update_employee_info(employee.id)
                updated_count += 1
            except Exception as e:
                # تسجيل الخطأ ومتابعة العملية
                _logger.warning(f"Failed to update employee info for {employee.name}: {e}")
                continue
        
        return updated_count

    def get_employee_public_info(self, employee_id):
        """
        الحصول على المعلومات العامة للموظف
        """
        if not employee_id:
            return {}
            
        info = self.search([('employee_id', '=', employee_id)], limit=1)
        if not info:
            # إنشاء المعلومات إذا لم تكن موجودة
            info = self.create_or_update_employee_info(employee_id)
            
        if info:
            return {
                'name': info.employee_name,
                'age': info.employee_age,
                'marital_status': info.employee_marital_status,
                'blood_type': info.employee_blood_type,
                'mobile': info.employee_mobile,
                'email': info.employee_email,
                'job_title': info.employee_job_title,
                'department': info.employee_department,
                'employee_number': info.employee_number,
            }
        
        return {}


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    @api.model_create_multi
    def create(self, vals_list):
        """
        تحديث المعلومات العامة عند إنشاء موظف جديد
        """
        employees = super().create(vals_list)
        for employee in employees:
            try:
                self.env['employee.public.info'].create_or_update_employee_info(employee.id)
            except Exception:
                # تجاهل الأخطاء لتجنب توقف عملية الإنشاء
                pass
        return employees

    def write(self, vals):
        """
        تحديث المعلومات العامة عند تعديل بيانات الموظف
        """
        result = super().write(vals)
        
        # التحقق من وجود حقول مهمة في التحديث
        important_fields = {'name', 'birthday', 'marital', 'bloodtype', 'mobile_phone', 
                          'work_email', 'job_title', 'job_id', 'department_id', 'int_id'}
        
        if any(field in vals for field in important_fields):
            for employee in self:
                try:
                    self.env['employee.public.info'].create_or_update_employee_info(employee.id)
                except Exception:
                    # تجاهل الأخطاء لتجنب توقف عملية التحديث
                    pass
        
        return result

    def unlink(self):
        """
        حذف المعلومات العامة عند حذف الموظف
        """
        # حذف المعلومات العامة المرتبطة
        public_infos = self.env['employee.public.info'].search([('employee_id', 'in', self.ids)])
        public_infos.unlink()
        
        return super().unlink()
