# -*- coding: utf-8 -*-
{
    'name': 'Medical Insurance',
    'category': 'Human Resources/Medical Insurance',
    'summary': 'Medical Insurance Management for Employees',
    'sequence': 10,
    'version': '********.0',
    'description': """
        Medical Insurance Management for Employees
        - Manage employee medical insurance
        - Different coverage limits based on marital status
        - 80% medication reimbursement
        - Spending caps per employee
        - Family member management for married employees with dependents
        - Medical facilities and departments management
    """,
    'depends': ['hr', 'web'],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'views/medical_insurance_views.xml',
        'views/medical_claim_views.xml',
        'views/hr_employee_views.xml',
        'views/hr_employee_public_access_views.xml',
        'views/employee_public_info_views.xml',
        'views/medical_facilities_views.xml',
        'views/employee_dashboard_views.xml',
        'views/menu_views.xml',
        'data/medical_insurance_data.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'medical_insurance/static/src/js/medical_dashboard.js',
            'medical_insurance/static/src/js/medical_dashboard_form.js',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
}
