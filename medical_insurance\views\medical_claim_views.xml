<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Medical Claim Views -->
    <record id="view_medical_claim_form" model="ir.ui.view">
        <field name="name">medical.claim.form</field>
        <field name="model">medical.claim</field>
        <field name="arch" type="xml">
            <form string="Medical Claim">
                <header>
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft" groups="medical_insurance.group_medical_insurance_user"/>
                    <button name="action_approve" string="Approve" type="object" class="oe_highlight" states="submitted" groups="medical_insurance.group_medical_insurance_manager"/>
                    <button name="action_reject" string="Reject" type="object" states="submitted" groups="medical_insurance.group_medical_insurance_manager"/>
                    <button name="action_pay" string="Mark as Paid" type="object" class="oe_highlight" states="approved" groups="medical_insurance.group_medical_insurance_manager"/>
                    <button name="action_reset_to_draft" string="Reset to Draft" type="object" states="rejected" groups="medical_insurance.group_medical_insurance_manager"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,approved,paid"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="employee_id" attrs="{'readonly': [('state', '!=', 'draft')]}" options="{'no_create': True, 'no_open': True}"/>
                            <field name="employee_number"/>
                            <field name="date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="claim_type" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="facility_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="department_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="beneficiary_type" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="family_member_id" attrs="{'invisible': [('beneficiary_type', '!=', 'family_member')], 'required': [('beneficiary_type', '=', 'family_member')], 'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="total_amount" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="reimbursement_rate" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="reimbursed_amount" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </page>
                        <page string="Attachments">
                            <field name="attachment_ids" widget="many2many_binary" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </page>
                        <page string="Cap Information" autofocus="autofocus">
                            <div class="alert alert-info" role="alert" attrs="{'invisible': [('employee_id', '=', False)]}">
                                <strong>Employee Insurance Information</strong>
                                <button name="action_refresh_cap_info" string="Refresh" type="object"
                                        class="btn btn-sm btn-primary float-right" icon="fa-refresh" groups="medical_insurance.group_medical_insurance_user"/>
                            </div>
                            <group>
                                <group>
                                    <field name="company_currency_id" invisible="1"/>
                                    <field name="fiscal_year_id" readonly="1"/>
                                    <field name="employee_balance_id" readonly="1" attrs="{'invisible': [('employee_balance_id', '=', False)]}"/>
                                    <field name="employee_cap" widget="monetary" options="{'currency_field': 'company_currency_id'}" decoration-bf="1"/>
                                    <field name="previous_claims_amount" widget="monetary" options="{'currency_field': 'company_currency_id'}" decoration-bf="1"/>
                                    <field name="remaining_balance" widget="monetary" options="{'currency_field': 'company_currency_id'}" decoration-bf="1" decoration-danger="remaining_balance == 0" decoration-warning="remaining_balance &lt; (employee_cap * 0.2)" decoration-success="remaining_balance &gt;= (employee_cap * 0.2)"/>
                                </group>
                                <group>
                                    <field name="exceeds_cap" invisible="1"/>
                                    <div class="alert alert-danger" role="alert" attrs="{'invisible': [('exceeds_cap', '=', False)]}">
                                        <strong>Warning!</strong> This claim exceeds the employee's remaining balance.
                                    </div>
                                    <div class="alert alert-success" role="alert" attrs="{'invisible': ['|', ('exceeds_cap', '=', True), ('employee_id', '=', False)]}">
                                        <strong>Info:</strong> This claim is within the employee's remaining balance.
                                    </div>
                                    <div class="alert alert-warning" role="alert" attrs="{'invisible': [('fiscal_year_id', '!=', False)]}">
                                        <strong>Note:</strong> No fiscal year found for this claim date. Using default cap values. Please create a fiscal year that includes this date.
                                    </div>
                                    <div class="alert alert-info" role="alert" attrs="{'invisible': ['|', ('fiscal_year_id', '=', False), ('employee_balance_id', '!=', False)]}">
                                        <strong>Info:</strong> Using fiscal year but no employee balance found. A balance will be created when the claim is processed.
                                    </div>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_medical_claim_tree" model="ir.ui.view">
        <field name="name">medical.claim.tree</field>
        <field name="model">medical.claim</field>
        <field name="arch" type="xml">
            <tree string="Medical Claims" decoration-info="state == 'draft'" decoration-muted="state == 'rejected'" decoration-success="state == 'paid'">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="date"/>
                <field name="beneficiary_type"/>
                <field name="family_member_id"/>
                <field name="claim_type"/>
                <field name="facility_id"/>
                <field name="department_id"/>
                <field name="total_amount" sum="Total"/>
                <field name="reimbursed_amount" sum="Reimbursed"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="view_medical_claim_search" model="ir.ui.view">
        <field name="name">medical.claim.search</field>
        <field name="model">medical.claim</field>
        <field name="arch" type="xml">
            <search string="Medical Claims">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="employee_number"/>
                <field name="family_member_id"/>
                <field name="facility_id"/>
                <field name="department_id"/>
                <field name="fiscal_year_id"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Submitted" name="submitted" domain="[('state', '=', 'submitted')]"/>
                <filter string="Approved" name="approved" domain="[('state', '=', 'approved')]"/>
                <filter string="Rejected" name="rejected" domain="[('state', '=', 'rejected')]"/>
                <filter string="Paid" name="paid" domain="[('state', '=', 'paid')]"/>
                <separator/>
                <filter string="Employee Claims" name="employee_claims" domain="[('beneficiary_type', '=', 'employee')]"/>
                <filter string="Family Member Claims" name="family_claims" domain="[('beneficiary_type', '=', 'family_member')]"/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="employee" domain="[]" context="{'group_by': 'employee_id'}"/>
                    <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                    <filter string="Claim Type" name="claim_type" domain="[]" context="{'group_by': 'claim_type'}"/>
                    <filter string="Claim Date" name="date" domain="[]" context="{'group_by': 'date'}"/>
                    <filter string="Fiscal Year" name="fiscal_year" domain="[]" context="{'group_by': 'fiscal_year_id'}"/>
                    <filter string="Beneficiary Type" name="beneficiary_type" domain="[]" context="{'group_by': 'beneficiary_type'}"/>
                    <filter string="Medical Facility" name="facility" domain="[]" context="{'group_by': 'facility_id'}"/>
                    <filter string="Department" name="department" domain="[]" context="{'group_by': 'department_id'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record id="view_medical_claim_calendar" model="ir.ui.view">
        <field name="name">medical.claim.calendar</field>
        <field name="model">medical.claim</field>
        <field name="arch" type="xml">
            <calendar string="Medical Claims" date_start="date" color="employee_id">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="reimbursed_amount"/>
                <field name="state"/>
            </calendar>
        </field>
    </record>

    <record id="view_medical_claim_pivot" model="ir.ui.view">
        <field name="name">medical.claim.pivot</field>
        <field name="model">medical.claim</field>
        <field name="arch" type="xml">
            <pivot string="Medical Claims Analysis">
                <field name="employee_id" type="row"/>
                <field name="date" type="col"/>
                <field name="total_amount" type="measure"/>
                <field name="reimbursed_amount" type="measure"/>
            </pivot>
        </field>
    </record>

    <record id="action_medical_claim" model="ir.actions.act_window">
        <field name="name">Medical Claims</field>
        <field name="res_model">medical.claim</field>
        <field name="view_mode">tree,form,calendar,pivot</field>
        <field name="context">{'search_default_employee_id': active_id if context.get('active_model') == 'hr.employee' else False}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new medical claim
            </p>
            <p>
                Create medical claims for employees and their family members.
            </p>
        </field>
    </record>

    <!-- Employee Portal Action -->
    <record id="action_my_medical_claim" model="ir.actions.act_window">
        <field name="name">My Medical Claims</field>
        <field name="res_model">medical.claim</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('employee_id.user_id', '=', uid)]</field>
        <field name="context">{'create': False}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No medical claims found
            </p>
            <p>
                Here you can view your medical claims.
            </p>
        </field>
    </record>
</odoo>
