<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Medical Insurance Card Form View -->
    <record id="view_medical_insurance_card_form" model="ir.ui.view">
        <field name="name">medical.insurance.card.form</field>
        <field name="model">medical.insurance.card</field>
        <field name="arch" type="xml">
            <form string="Medical Insurance Card">
                <header>
                    <button name="action_activate" string="Activate" type="object"
                            class="oe_highlight" states="draft"/>
                    <button name="action_draft" string="Set to Draft" type="object"
                            states="cancelled,expired"/>
                    <button name="action_cancel" string="Cancel" type="object"
                            states="draft,active"/>
                    <button name="action_renew_card" string="Renew Card" type="object"
                            class="oe_highlight" states="active,expired"/>
                    <button name="action_print_card" string="Print Card" type="object"
                            class="oe_highlight" states="active"/>
                    <field name="state" widget="statusbar"
                           statusbar_visible="draft,active,expired"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_family_cards" type="object" class="oe_stat_button"
                                icon="fa-id-card-o" attrs="{'invisible': [('family_card_count', '=', 0)]}">
                            <field name="family_card_count" widget="statinfo" string="Family Cards"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="card_number"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="employee_id" options="{'no_create': True, 'no_open': True}"/>
                            <field name="employee_number"/>
                            <field name="employee_job"/>
                            <field name="employee_department"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                        <group>
                            <field name="issue_date"/>
                            <field name="expiry_date"/>
                            <field name="active" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Employee Information">
                            <group>
                                <field name="employee_image" widget="image" class="oe_avatar"/>
                                <field name="employee_name"/>
                            </group>
                        </page>
                        <page string="Family Member Cards" attrs="{'invisible': [('family_card_count', '=', 0)]}">
                            <field name="family_card_ids" readonly="1">
                                <tree>
                                    <field name="card_number"/>
                                    <field name="member_name"/>
                                    <field name="relationship"/>
                                    <field name="issue_date"/>
                                    <field name="expiry_date"/>
                                    <field name="state"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Notes">
                            <field name="notes"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Medical Insurance Card Tree View -->
    <record id="view_medical_insurance_card_tree" model="ir.ui.view">
        <field name="name">medical.insurance.card.tree</field>
        <field name="model">medical.insurance.card</field>
        <field name="arch" type="xml">
            <tree string="Medical Insurance Cards" decoration-danger="state=='expired'" decoration-success="state=='active'" decoration-info="state=='draft'" decoration-muted="state=='cancelled'">
                <field name="card_number"/>
                <field name="employee_id"/>
                <field name="employee_number"/>
                <field name="issue_date"/>
                <field name="expiry_date"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <!-- Medical Insurance Card Search View -->
    <record id="view_medical_insurance_card_search" model="ir.ui.view">
        <field name="name">medical.insurance.card.search</field>
        <field name="model">medical.insurance.card</field>
        <field name="arch" type="xml">
            <search string="Search Medical Insurance Cards">
                <field name="card_number"/>
                <field name="employee_id"/>
                <field name="employee_number"/>
                <separator/>
                <filter string="Active" name="active" domain="[('state', '=', 'active')]"/>
                <filter string="Expired" name="expired" domain="[('state', '=', 'expired')]"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Cancelled" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                <separator/>
                <filter string="Expiring This Month" name="expiring_this_month"
                        domain="[('expiry_date', '&gt;=', context_today().strftime('%Y-%m-01')),
                                ('expiry_date', '&lt;=', (context_today() + relativedelta(months=1, day=1, days=-1)).strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="employee" context="{'group_by': 'employee_id'}"/>
                    <filter string="Status" name="status" context="{'group_by': 'state'}"/>
                    <filter string="Issue Date" name="issue_date" context="{'group_by': 'issue_date'}"/>
                    <filter string="Expiry Date" name="expiry_date" context="{'group_by': 'expiry_date'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Medical Insurance Family Card Form View -->
    <record id="view_medical_insurance_family_card_form" model="ir.ui.view">
        <field name="name">medical.insurance.family.card.form</field>
        <field name="model">medical.insurance.family.card</field>
        <field name="arch" type="xml">
            <form string="Family Member Insurance Card">
                <header>
                    <button name="action_activate" string="Activate" type="object"
                            class="oe_highlight" states="draft"/>
                    <button name="action_draft" string="Set to Draft" type="object"
                            states="cancelled,expired"/>
                    <button name="action_cancel" string="Cancel" type="object"
                            states="draft,active"/>
                    <button name="action_print_card" string="Print Card" type="object"
                            class="oe_highlight" states="active"/>
                    <field name="state" widget="statusbar"
                           statusbar_visible="draft,active,expired"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="card_number"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="card_id"/>
                            <field name="employee_id" invisible="1"/>
                            <field name="family_member_id"/>
                            <field name="member_name"/>
                            <field name="relationship"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                        <group>
                            <field name="issue_date"/>
                            <field name="expiry_date"/>
                            <field name="active" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Notes">
                            <field name="notes"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Medical Insurance Family Card Tree View -->
    <record id="view_medical_insurance_family_card_tree" model="ir.ui.view">
        <field name="name">medical.insurance.family.card.tree</field>
        <field name="model">medical.insurance.family.card</field>
        <field name="arch" type="xml">
            <tree string="Family Member Insurance Cards" decoration-danger="state=='expired'" decoration-success="state=='active'" decoration-info="state=='draft'" decoration-muted="state=='cancelled'">
                <field name="card_number"/>
                <field name="card_id"/>
                <field name="member_name"/>
                <field name="relationship"/>
                <field name="issue_date"/>
                <field name="expiry_date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Actions -->
    <record id="action_medical_insurance_card" model="ir.actions.act_window">
        <field name="name">Insurance Cards</field>
        <field name="res_model">medical.insurance.card</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new medical insurance card
            </p>
            <p>
                Create and manage medical insurance cards for employees and their family members.
            </p>
        </field>
    </record>

    <record id="action_medical_insurance_family_card" model="ir.actions.act_window">
        <field name="name">Family Member Cards</field>
        <field name="res_model">medical.insurance.family.card</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new family member insurance card
            </p>
            <p>
                Create and manage medical insurance cards for family members of employees.
            </p>
        </field>
    </record>
</odoo>
