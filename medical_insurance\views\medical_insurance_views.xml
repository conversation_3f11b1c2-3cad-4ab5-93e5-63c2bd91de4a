<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Medical Insurance Configuration Views -->
    <record id="view_medical_insurance_config_form" model="ir.ui.view">
        <field name="name">medical.insurance.config.form</field>
        <field name="model">medical.insurance.config</field>
        <field name="arch" type="xml">
            <form string="Medical Insurance Configuration">
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="reimbursement_rate"/>
                        </group>
                        <group>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Spending Caps">
                            <group>
                                <field name="single_cap"/>
                                <field name="married_with_dependents_cap"/>
                            </group>
                        </page>
                        <page string="Fiscal Years">
                            <div class="oe_button_box" name="button_box">
                                <button name="action_create_fiscal_year" 
                                        string="Create New Fiscal Year" 
                                        type="object" 
                                        class="oe_highlight"
                                        groups="medical_insurance.group_medical_insurance_manager"/>
                                <button name="action_close_current_fiscal_year" 
                                        string="Close Current Fiscal Year" 
                                        type="object" 
                                        class="btn-warning"
                                        attrs="{'invisible': [('current_fiscal_year_id', '=', False)]}"
                                        groups="medical_insurance.group_medical_insurance_manager"/>
                            </div>
                            <group>
                                <field name="current_fiscal_year_id" readonly="1"/>
                            </group>
                            <field name="fiscal_year_ids">
                                <tree>
                                    <field name="name"/>
                                    <field name="start_date"/>
                                    <field name="end_date"/>
                                    <field name="state" decoration-success="state == 'open'" decoration-info="state == 'draft'" decoration-muted="state == 'closed'" decoration-danger="state == 'archived'"/>
                                </tree>
                            </field>
                        </page>
                        <page string="Maintenance">
                            <group>
                                <button name="cleanup_generic_spouse_records"
                                        string="Remove Generic Spouse Records"
                                        type="object"
                                        class="oe_highlight"
                                        groups="medical_insurance.group_medical_insurance_manager"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_medical_insurance_config_tree" model="ir.ui.view">
        <field name="name">medical.insurance.config.tree</field>
        <field name="model">medical.insurance.config</field>
        <field name="arch" type="xml">
            <tree string="Medical Insurance Configurations">
                <field name="name"/>
                <field name="reimbursement_rate"/>
                <field name="single_cap"/>
                <field name="married_with_dependents_cap"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <field name="active"/>
            </tree>
        </field>
    </record>

    <record id="view_medical_insurance_config_search" model="ir.ui.view">
        <field name="name">medical.insurance.config.search</field>
        <field name="model">medical.insurance.config</field>
        <field name="arch" type="xml">
            <search string="Medical Insurance Configuration">
                <field name="name"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_medical_insurance_config" model="ir.actions.act_window">
        <field name="name">Insurance Configuration</field>
        <field name="res_model">medical.insurance.config</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new medical insurance configuration
            </p>
            <p>
                Define insurance caps and reimbursement rates.
            </p>
        </field>
    </record>

    <!-- Family Member Views -->
    <record id="view_medical_family_member_form" model="ir.ui.view">
        <field name="name">medical.family.member.form</field>
        <field name="model">medical.family.member</field>
        <field name="arch" type="xml">
            <form string="Family Member">
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="employee_id"/>
                            <field name="relationship"/>
                        </group>
                        <group>
                            <field name="birth_date"/>
                            <field name="active"/>
                            <field name="company_id" groups="base.group_multi_company" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Notes">
                            <field name="notes"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_medical_family_member_tree" model="ir.ui.view">
        <field name="name">medical.family.member.tree</field>
        <field name="model">medical.family.member</field>
        <field name="arch" type="xml">
            <tree string="Family Members">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="relationship"/>
                <field name="birth_date"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="view_medical_family_member_search" model="ir.ui.view">
        <field name="name">medical.family.member.search</field>
        <field name="model">medical.family.member</field>
        <field name="arch" type="xml">
            <search string="Family Members">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="relationship"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="employee" domain="[]" context="{'group_by': 'employee_id'}"/>
                    <filter string="Relationship" name="relationship" domain="[]" context="{'group_by': 'relationship'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_medical_family_member" model="ir.actions.act_window">
        <field name="name">Family Members</field>
        <field name="res_model">medical.family.member</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new family member
            </p>
            <p>
                Add family members for employees with dependents.
            </p>
        </field>
    </record>

    <!-- Medical Insurance Fiscal Year Views -->
    <record id="view_medical_insurance_fiscal_year_form" model="ir.ui.view">
        <field name="name">medical.insurance.fiscal.year.form</field>
        <field name="model">medical.insurance.fiscal.year</field>
        <field name="arch" type="xml">
            <form string="Fiscal Year">
                <header>
                    <button name="action_open" string="Open Fiscal Year" type="object" 
                            class="oe_highlight" attrs="{'invisible': [('state', '!=', 'draft')]}"
                            groups="medical_insurance.group_medical_insurance_manager"/>
                    <button name="action_close" string="Close Fiscal Year" type="object" 
                            class="btn-warning" attrs="{'invisible': [('state', '!=', 'open')]}"
                            groups="medical_insurance.group_medical_insurance_manager"/>
                    <button name="action_archive" string="Archive Fiscal Year" type="object" 
                            attrs="{'invisible': [('state', '!=', 'closed')]}"
                            groups="medical_insurance.group_medical_insurance_manager"/>
                    <button name="action_reset_to_draft" string="Reset to Draft" type="object" 
                            attrs="{'invisible': [('state', 'in', ['open', 'archived'])]}"
                            groups="medical_insurance.group_medical_insurance_manager"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,open,closed,archived"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only"/>
                        <h1><field name="name"/></h1>
                    </div>
                    <group>
                        <group>
                            <field name="config_id"/>
                            <field name="company_id" groups="base.group_multi_company" readonly="1"/>
                        </group>
                        <group>
                            <field name="start_date"/>
                            <field name="end_date"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Employee Balances" attrs="{'invisible': [('state', '=', 'draft')]}">
                            <field name="employee_balance_ids">
                                <tree>
                                    <field name="employee_id"/>
                                    <field name="initial_balance"/>
                                    <field name="current_balance"/>
                                    <field name="used_amount"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_medical_insurance_fiscal_year_tree" model="ir.ui.view">
        <field name="name">medical.insurance.fiscal.year.tree</field>
        <field name="model">medical.insurance.fiscal.year</field>
        <field name="arch" type="xml">
            <tree string="Fiscal Years" decoration-success="state == 'open'" decoration-info="state == 'draft'" decoration-muted="state == 'closed'" decoration-danger="state == 'archived'">
                <field name="name"/>
                <field name="config_id"/>
                <field name="start_date"/>
                <field name="end_date"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="view_medical_insurance_fiscal_year_search" model="ir.ui.view">
        <field name="name">medical.insurance.fiscal.year.search</field>
        <field name="model">medical.insurance.fiscal.year</field>
        <field name="arch" type="xml">
            <search string="Fiscal Years">
                <field name="name"/>
                <field name="config_id"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Open" name="open" domain="[('state', '=', 'open')]"/>
                <filter string="Closed" name="closed" domain="[('state', '=', 'closed')]"/>
                <filter string="Archived" name="archived" domain="[('state', '=', 'archived')]"/>
                <group expand="0" string="Group By">
                    <filter string="Configuration" name="config" domain="[]" context="{'group_by': 'config_id'}"/>
                    <filter string="Status" name="status" domain="[]" context="{'group_by': 'state'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_medical_insurance_fiscal_year" model="ir.actions.act_window">
        <field name="name">Fiscal Years</field>
        <field name="res_model">medical.insurance.fiscal.year</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new fiscal year
            </p>
            <p>
                Define fiscal years for medical insurance benefits.
            </p>
        </field>
    </record>

    <!-- Medical Insurance Employee Balance Views -->
    <record id="view_medical_insurance_employee_balance_form" model="ir.ui.view">
        <field name="name">medical.insurance.employee.balance.form</field>
        <field name="model">medical.insurance.employee.balance</field>
        <field name="arch" type="xml">
            <form string="Employee Balance">
                <sheet>
                    <group>
                        <group>
                            <field name="employee_id"/>
                            <field name="fiscal_year_id"/>
                            <field name="company_id" groups="base.group_multi_company" readonly="1"/>
                        </group>
                        <group>
                            <field name="initial_balance"/>
                            <field name="current_balance"/>
                            <field name="used_amount"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_medical_insurance_employee_balance_tree" model="ir.ui.view">
        <field name="name">medical.insurance.employee.balance.tree</field>
        <field name="model">medical.insurance.employee.balance</field>
        <field name="arch" type="xml">
            <tree string="Employee Balances">
                <field name="employee_id"/>
                <field name="fiscal_year_id"/>
                <field name="initial_balance"/>
                <field name="current_balance"/>
                <field name="used_amount"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </tree>
        </field>
    </record>

    <record id="view_medical_insurance_employee_balance_search" model="ir.ui.view">
        <field name="name">medical.insurance.employee.balance.search</field>
        <field name="model">medical.insurance.employee.balance</field>
        <field name="arch" type="xml">
            <search string="Employee Balances">
                <field name="employee_id"/>
                <field name="fiscal_year_id"/>
                <group expand="0" string="Group By">
                    <filter string="Employee" name="employee" domain="[]" context="{'group_by': 'employee_id'}"/>
                    <filter string="Fiscal Year" name="fiscal_year" domain="[]" context="{'group_by': 'fiscal_year_id'}"/>
                    <filter string="Company" name="company" domain="[]" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_medical_insurance_employee_balance" model="ir.actions.act_window">
        <field name="name">Employee Balances</field>
        <field name="res_model">medical.insurance.employee.balance</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No employee balances found
            </p>
            <p>
                Employee balances are created automatically when a fiscal year is opened.
            </p>
        </field>
    </record>

</odoo>
