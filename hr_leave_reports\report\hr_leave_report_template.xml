<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- قالب تقرير الإجازات المتقدم -->
        <template id="hr_leave_report_template">
            <t t-call="web.html_container">
                <t t-call="web.external_layout">
                    <div class="page" style="direction: rtl; text-align: right;">
                        <!-- رأس التقرير -->
                        <div class="row mb-4">
                            <div class="col-12 text-center">
                                <h2 style="color: #2E86AB; font-weight: bold; margin-bottom: 20px; border-bottom: 3px solid #2E86AB; padding-bottom: 10px;">
                                    Leave Advanced Report
                                </h2>
                            </div>
                        </div>

                        <!-- معلومات التقرير -->
                        <div class="row mb-4" style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
                            <div class="col-6">
                                <table class="table table-borderless" style="font-size: 14px; margin-bottom: 0;">
                                    <tr>
                                        <td style="font-weight: bold; width: 35%; color: #2E86AB;">Period:</td>
                                        <td>From <span t-esc="date_from"/> To <span t-esc="date_to"/></td>
                                    </tr>
                                    <tr>
                                        <td style="font-weight: bold; color: #2E86AB;">Employee:</td>
                                        <td><span t-esc="employee_name"/></td>
                                    </tr>
                                    <!-- <tr>
                                        <td style="font-weight: bold; color: #2E86AB;">Leave State:</td>
                                        <td><span t-esc="state_filter"/></td>
                                    </tr> -->
                                </table>
                            </div>
                            <div class="col-6 text-left">
                                <table class="table table-borderless" style="font-size: 14px; margin-bottom: 0;">
                                    <tr>
                                        <td style="font-weight: bold; width: 45%; color: #2E86AB;">Report Date:</td>
                                        <td><span t-esc="report_date"/></td>
                                    </tr>
                                    <tr>
                                        <td style="font-weight: bold; color: #2E86AB;">Total Leaves:</td>
                                        <td><span t-esc="total_leaves"/> leaves</td>
                                    </tr>
                                    <!-- <tr>
                                        <td style="font-weight: bold; color: #2E86AB;">Total Days:</td>
                                        <td><span t-esc="total_days"/> days</td>
                                    </tr> -->
                                </table>
                            </div>
                        </div>

                        <!-- جدول البيانات -->
                        <div class="row">
                            <div class="col-12">
                                <table class="table table-bordered table-striped" style="font-size: 11px; border: 2px solid #2E86AB;">
                                    <thead style="background: linear-gradient(135deg, #2E86AB 0%, #1a5f7a 100%); color: white;">
                                        <tr>
                                            <th class="text-center" style="width: 4%; padding: 12px 8px; border: 1px solid #fff;">#</th>
                                            <th class="text-center" style="width: 10%; padding: 12px 8px; border: 1px solid #fff;">Employee ID</th>
                                            <th class="text-center" style="width: 18%; padding: 12px 8px; border: 1px solid #fff;">Employee Name</th>
                                            <th class="text-center" style="width: 12%; padding: 12px 8px; border: 1px solid #fff;">Department</th>
                                            <th class="text-center" style="width: 12%; padding: 12px 8px; border: 1px solid #fff;">Leave Type</th>
                                            <th class="text-center" style="width: 10%; padding: 12px 8px; border: 1px solid #fff;">Start Date</th>
                                            <th class="text-center" style="width: 10%; padding: 12px 8px; border: 1px solid #fff;">End Date</th>
                                            <th class="text-center" style="width: 8%; padding: 12px 8px; border: 1px solid #fff;">Days</th>
                                            <th class="text-center" style="width: 10%; padding: 12px 8px; border: 1px solid #fff;">State</th>
                                            <th class="text-center" style="width: 6%; padding: 12px 8px; border: 1px solid #fff;">Request Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-set="counter" t-value="1"/>
                                        <t t-foreach="leave_data" t-as="leave">
                                            <tr style="border-bottom: 1px solid #dee2e6;">
                                                <td class="text-center" style="padding: 10px 8px; background-color: #f8f9fa; font-weight: bold;">
                                                    <span t-esc="counter"/>
                                                    <t t-set="counter" t-value="counter + 1"/>
                                                </td>
                                                <td class="text-center" style="padding: 10px 8px;">
                                                    <span t-esc="leave['employee_code']"/>
                                                </td>
                                                <td style="padding: 10px 8px; font-weight: 500;">
                                                    <span t-esc="leave['employee_name']"/>
                                                </td>
                                                <td class="text-center" style="padding: 10px 8px;">
                                                    <span t-esc="leave['department']"/>
                                                </td>
                                                <td class="text-center" style="padding: 10px 8px;">
                                                    <span t-esc="leave['leave_type']"/>
                                                </td>
                                                <td class="text-center" style="padding: 10px 8px;">
                                                    <span t-esc="leave['date_from']"/>
                                                </td>
                                                <td class="text-center" style="padding: 10px 8px;">
                                                    <span t-esc="leave['date_to']"/>
                                                </td>
                                                <td class="text-center" style="padding: 10px 8px; font-weight: bold; color: #2E86AB;">
                                                    <span t-esc="leave['number_of_days']"/>
                                                </td>
                                                <td class="text-center" style="padding: 10px 8px;">
                                                    <span t-esc="leave['state']"/>
                                                </td>
                                                <td class="text-center" style="padding: 10px 8px; font-size: 10px;">
                                                    <span t-esc="leave['request_date']"/>
                                                </td>
                                            </tr>
                                        </t>

                                        <!-- رسالة في حالة عدم وجود بيانات -->
                                        <t t-if="not leave_data">
                                            <tr>
                                                <td colspan="10" class="text-center" style="padding: 40px; color: #666; font-size: 16px; background-color: #f8f9fa;">
                                                    <i class="fa fa-info-circle" style="margin-left: 10px; color: #2E86AB;"></i>
                                                    No leaves found in the specified period
                                                </td>
                                            </tr>
                                        </t>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- ملخص التقرير -->
                        <t t-if="leave_data">
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 10px; border: 2px solid #2E86AB;">
                                        <h5 style="color: #2E86AB; margin-bottom: 15px; text-align: center; font-weight: bold;">
                                            <i class="fa fa-chart-bar" style="margin-left: 10px;"></i>
                                            Report Summary
                                        </h5>

                                        <!-- الإحصائيات العامة -->
                                        <div class="row text-center mb-3">
                                            <div class="col-4">
                                                <div style="background-color: white; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
                                                    <strong style="color: #2E86AB; font-size: 16px;">Total Leaves:</strong><br/>
                                                    <span style="font-size: 24px; font-weight: bold; color: #28a745;" t-esc="total_leaves"/>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div style="background-color: white; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
                                                    <strong style="color: #2E86AB; font-size: 16px;">Total Days:</strong><br/>
                                                    <span style="font-size: 24px; font-weight: bold; color: #dc3545;" t-esc="total_days"/>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div style="background-color: white; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
                                                    <strong style="color: #2E86AB; font-size: 16px;">Average Days:</strong><br/>
                                                    <span style="font-size: 24px; font-weight: bold; color: #ffc107;"
                                                          t-esc="round(total_days / total_leaves, 2) if total_leaves > 0 else 0"/>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- معلومات الرصيد (للموظف المحدد فقط) -->
                                        <t t-if="show_balance and balance_info">
                                            <div style="border-top: 2px solid #2E86AB; padding-top: 15px; margin-top: 15px;">
                                                <h6 style="color: #2E86AB; margin-bottom: 15px; text-align: center; font-weight: bold;">
                                                    <i class="fa fa-balance-scale" style="margin-left: 10px;"></i>
                                                    Leave Balance Information
                                                    <span t-if="balance_info.get('leave_type_name')" style="font-size: 14px; color: #666;">
                                                        (<span t-esc="balance_info['leave_type_name']"/>)
                                                    </span>
                                                </h6>
                                                <div class="row text-center">
                                                    <div class="col-4">
                                                        <div style="background-color: #e8f5e8; padding: 15px; border-radius: 8px; border: 1px solid #28a745;">
                                                            <strong style="color: #28a745; font-size: 14px;">Balance Before Request:</strong><br/>
                                                            <span style="font-size: 20px; font-weight: bold; color: #28a745;"
                                                                  t-esc="balance_info.get('balance_before', 0)"/> Days
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffc107;">
                                                            <strong style="color: #856404; font-size: 14px;">Requested Days:</strong><br/>
                                                            <span style="font-size: 20px; font-weight: bold; color: #856404;"
                                                                  t-esc="balance_info.get('requested_days', 0)"/> Days
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div style="background-color: #f8d7da; padding: 15px; border-radius: 8px; border: 1px solid #dc3545;">
                                                            <strong style="color: #721c24; font-size: 14px;">Balance After Request:</strong><br/>
                                                            <span style="font-size: 20px; font-weight: bold; color: #721c24;"
                                                                  t-esc="balance_info.get('balance_after', 0)"/> Days
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </t>

                                        <!-- معلومات الموافقة (للموظف المحدد فقط) -->
                                        <t t-if="show_approval and approval_info and approval_info.get('show_approval')">
                                            <div style="border-top: 2px solid #2E86AB; padding-top: 15px; margin-top: 15px;">
                                                <h6 style="color: #2E86AB; margin-bottom: 15px; text-align: center; font-weight: bold;">
                                                    <i class="fa fa-check-circle" style="margin-left: 10px;"></i>
                                                    Approval Information
                                                </h6>

                                                <!-- جدول معلومات الموافقة -->
                                                <div style="background-color: white; border-radius: 8px; border: 1px solid #dee2e6; overflow: hidden;">
                                                    <table class="table table-sm mb-0" style="font-size: 12px;">
                                                        <thead style="background-color: #2E86AB; color: white;">
                                                            <tr>
                                                                <th class="text-center" style="padding: 8px; border: none;">Leave Type</th>
                                                                <th class="text-center" style="padding: 8px; border: none;">Period</th>
                                                                <th class="text-center" style="padding: 8px; border: none;">Days</th>
                                                                <th class="text-center" style="padding: 8px; border: none;">Approved By</th>
                                                                <th class="text-center" style="padding: 8px; border: none;">Validated By</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <t t-foreach="approval_info.get('approvals', [])" t-as="approval">
                                                                <tr style="border-bottom: 1px solid #f0f0f0;">
                                                                    <td class="text-center" style="padding: 8px; font-weight: 500;">
                                                                        <span t-esc="approval['leave_type']"/>
                                                                    </td>
                                                                    <td class="text-center" style="padding: 8px;">
                                                                        <span t-esc="approval['date_from']"/> - <span t-esc="approval['date_to']"/>
                                                                    </td>
                                                                    <td class="text-center" style="padding: 8px; font-weight: bold; color: #2E86AB;">
                                                                        <span t-esc="approval['number_of_days']"/>
                                                                    </td>
                                                                    <td class="text-center" style="padding: 8px;">
                                                                        <span t-if="approval['approved_by']"
                                                                              style="background-color: #d4edda; color: #155724; padding: 2px 8px; border-radius: 4px; font-size: 11px;">
                                                                            <i class="fa fa-user-check" style="margin-left: 3px;"></i>
                                                                            <span t-esc="approval['approved_by']"/>
                                                                        </span>
                                                                        <span t-if="not approval['approved_by']" style="color: #6c757d; font-style: italic;">
                                                                            N/A
                                                                        </span>
                                                                    </td>
                                                                    <td class="text-center" style="padding: 8px;">
                                                                        <span t-if="approval['validated_by']"
                                                                              style="background-color: #cce5ff; color: #004085; padding: 2px 8px; border-radius: 4px; font-size: 11px;">
                                                                            <i class="fa fa-stamp" style="margin-left: 3px;"></i>
                                                                            <span t-esc="approval['validated_by']"/>
                                                                        </span>
                                                                        <span t-if="not approval['validated_by']" style="color: #6c757d; font-style: italic;">
                                                                            N/A
                                                                        </span>
                                                                    </td>
                                                                </tr>
                                                            </t>

                                                            <!-- رسالة في حالة عدم وجود موافقات -->
                                                            <t t-if="not approval_info.get('approvals')">
                                                                <tr>
                                                                    <td colspan="5" class="text-center" style="padding: 20px; color: #666; font-style: italic;">
                                                                        No approval information available
                                                                    </td>
                                                                </tr>
                                                            </t>
                                                        </tbody>
                                                    </table>
                                                </div>

                                                <!-- ملخص الموافقات -->
                                                <div class="row mt-3 text-center">
                                                    <div class="col-12">
                                                        <div style="background-color: #f8f9fa; padding: 10px; border-radius: 6px; border: 1px solid #dee2e6;">
                                                            <small style="color: #2E86AB; font-weight: bold;">
                                                                Total Approved Leaves: <span t-esc="approval_info.get('total_approvals', 0)"/>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </t>
                                    </div>
                                </div>
                            </div>
                        </t>

                        <!-- تذييل التقرير -->
                        <!-- <div class="row mt-5">
                            <div class="col-12 text-center" style="font-size: 11px; color: #666; border-top: 1px solid #dee2e6; padding-top: 15px;">
                                <i class="fa fa-cog" style="margin-left: 5px;"></i>
                                Generated by HR Management System - Advanced Leave Reports
                            </div>
                        </div> -->
                    </div>
                </t>
            </t>
        </template>
    </data>
</odoo>
