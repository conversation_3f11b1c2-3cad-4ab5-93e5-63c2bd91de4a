odoo.define('medical_insurance.dashboard_charts', function (require) {
    "use strict";

    var AbstractField = require('web.AbstractField');
    var fieldRegistry = require('web.field_registry');
    var core = require('web.core');

    /**
     * Pie Chart Widget
     * 
     * This widget renders a pie chart using Chart.js
     */
    var PieChartWidget = AbstractField.extend({
        className: 'o_pie_chart',
        jsLibs: [
            '/web/static/lib/Chart/Chart.js',
        ],

        /**
         * @override
         */
        init: function () {
            this._super.apply(this, arguments);
            this.chart = null;
        },

        /**
         * @override
         */
        _render: function () {
            var self = this;
            if (!this.chart) {
                this.$el.empty();
                var canvas = $('<canvas/>');
                this.$el.append(canvas);

                var ctx = canvas[0].getContext('2d');
                
                try {
                    var data = JSON.parse(this.value || '{}');
                    if (data && data.labels && data.datasets) {
                        this.chart = new Chart(ctx, {
                            type: 'pie',
                            data: data,
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                legend: {
                                    position: 'right',
                                    labels: {
                                        fontFamily: '"Tajawal", sans-serif',
                                        fontSize: 12
                                    }
                                },
                                tooltips: {
                                    callbacks: {
                                        label: function(tooltipItem, data) {
                                            var dataset = data.datasets[tooltipItem.datasetIndex];
                                            var total = dataset.data.reduce(function(previousValue, currentValue) {
                                                return previousValue + currentValue;
                                            });
                                            var currentValue = dataset.data[tooltipItem.index];
                                            var percentage = Math.floor(((currentValue/total) * 100)+0.5);
                                            return data.labels[tooltipItem.index] + ': ' + currentValue + ' (' + percentage + '%)';
                                        }
                                    }
                                }
                            }
                        });
                    } else {
                        this.$el.append($('<div class="alert alert-info">').text('No data available'));
                    }
                } catch (e) {
                    this.$el.append($('<div class="alert alert-danger">').text('Error parsing chart data'));
                    console.error('Error parsing chart data:', e);
                }
            } else {
                try {
                    var data = JSON.parse(this.value || '{}');
                    if (data && data.labels && data.datasets) {
                        this.chart.data = data;
                        this.chart.update();
                    }
                } catch (e) {
                    console.error('Error updating chart data:', e);
                }
            }
        },

        /**
         * @override
         */
        destroy: function () {
            if (this.chart) {
                this.chart.destroy();
            }
            this._super.apply(this, arguments);
        },
    });

    /**
     * Bar Chart Widget
     * 
     * This widget renders a bar chart using Chart.js
     */
    var BarChartWidget = AbstractField.extend({
        className: 'o_bar_chart',
        jsLibs: [
            '/web/static/lib/Chart/Chart.js',
        ],

        /**
         * @override
         */
        init: function () {
            this._super.apply(this, arguments);
            this.chart = null;
        },

        /**
         * @override
         */
        _render: function () {
            var self = this;
            if (!this.chart) {
                this.$el.empty();
                var canvas = $('<canvas/>');
                this.$el.append(canvas);

                var ctx = canvas[0].getContext('2d');
                
                try {
                    var data = JSON.parse(this.value || '{}');
                    if (data && data.labels && data.datasets) {
                        this.chart = new Chart(ctx, {
                            type: 'bar',
                            data: data,
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                legend: {
                                    position: 'top',
                                    labels: {
                                        fontFamily: '"Tajawal", sans-serif',
                                        fontSize: 12
                                    }
                                },
                                scales: {
                                    yAxes: [{
                                        ticks: {
                                            beginAtZero: true
                                        }
                                    }]
                                }
                            }
                        });
                    } else {
                        this.$el.append($('<div class="alert alert-info">').text('No data available'));
                    }
                } catch (e) {
                    this.$el.append($('<div class="alert alert-danger">').text('Error parsing chart data'));
                    console.error('Error parsing chart data:', e);
                }
            } else {
                try {
                    var data = JSON.parse(this.value || '{}');
                    if (data && data.labels && data.datasets) {
                        this.chart.data = data;
                        this.chart.update();
                    }
                } catch (e) {
                    console.error('Error updating chart data:', e);
                }
            }
        },

        /**
         * @override
         */
        destroy: function () {
            if (this.chart) {
                this.chart.destroy();
            }
            this._super.apply(this, arguments);
        },
    });

    // Register the widgets
    fieldRegistry.add('pie_chart', PieChartWidget);
    fieldRegistry.add('bar_chart', BarChartWidget);

    return {
        PieChartWidget: PieChartWidget,
        BarChartWidget: BarChartWidget,
    };
});
