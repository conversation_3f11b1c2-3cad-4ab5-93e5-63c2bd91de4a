<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- تعديل واجهة الموظف لإضافة المعلومات الشخصية المتاحة للجميع -->
    <record id="view_employee_form_public_access" model="ir.ui.view">
        <field name="name">hr.employee.form.public.access</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_form"/>
        <field name="arch" type="xml">
            <!-- إضافة المعلومات الشخصية المتاحة للجميع -->
            <xpath expr="//page[@name='personal_information']" position="inside">
                <group string="Public Personal Information" name="public_personal_info">
                    <group>
                        <field name="public_age" string="العمر"/>
                        <field name="public_marital_status" string="الحالة الاجتماعية"/>
                    </group>
                </group>
            </xpath>
        </field>
    </record>

    <!-- تعديل واجهة الموظف العامة لإضافة المعلومات الشخصية -->
    <record id="view_employee_public_form_access" model="ir.ui.view">
        <field name="name">hr.employee.public.form.access</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_form"/>
        <field name="arch" type="xml">
            <!-- إضافة المعلومات الشخصية في واجهة الموظف العامة -->
            <xpath expr="//group[last()]" position="after">
                <group string="Personal Information" name="public_personal_info">
                    <group>
                        <field name="public_age" string="العمر"/>
                        <field name="public_marital_status" string="الحالة الاجتماعية"/>
                    </group>
                </group>
            </xpath>
        </field>
    </record>

    <!-- إضافة الحقول في قائمة الموظفين -->
    <record id="view_employee_tree_public_access" model="ir.ui.view">
        <field name="name">hr.employee.tree.public.access</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='work_email']" position="after">
                <field name="public_age" string="العمر" optional="hide"/>
                <field name="public_marital_status" string="الحالة الاجتماعية" optional="hide"/>
            </xpath>
        </field>
    </record>

    <!-- إضافة الحقول في قائمة الموظفين العامة -->
    <record id="view_employee_public_tree_access" model="ir.ui.view">
        <field name="name">hr.employee.public.tree.access</field>
        <field name="model">hr.employee.public</field>
        <field name="inherit_id" ref="hr.hr_employee_public_view_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='work_email']" position="after">
                <field name="public_age" string="العمر" optional="hide"/>
                <field name="public_marital_status" string="الحالة الاجتماعية" optional="hide"/>
            </xpath>
        </field>
    </record>

    <!-- إضافة الحقول في البحث -->
    <record id="view_employee_filter_public_access" model="ir.ui.view">
        <field name="name">hr.employee.search.public.access</field>
        <field name="model">hr.employee</field>
        <field name="inherit_id" ref="hr.view_employee_filter"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='name']" position="after">
                <field name="public_age" string="العمر"/>
                <field name="public_marital_status" string="الحالة الاجتماعية"/>
            </xpath>
            <xpath expr="//group[@expand='0']" position="inside">
                <filter name="group_by_marital" string="الحالة الاجتماعية" domain="[]" context="{'group_by':'public_marital_status'}"/>
            </xpath>
        </field>
    </record>
</odoo>
