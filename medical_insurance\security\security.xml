<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Medical Insurance Category -->
        <record model="ir.module.category" id="module_medical_insurance_category">
            <field name="name">Medical Insurance</field>
            <field name="description">Medical Insurance Management</field>
            <field name="sequence">50</field>
        </record>

        <!-- Medical Insurance Employee Portal Group -->
        <record id="group_medical_insurance_employee_portal" model="res.groups">
            <field name="name">Employee Portal</field>
            <field name="category_id" ref="module_medical_insurance_category"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">Employees can only view their own claims</field>
        </record>

        <!-- Medical Insurance User Group -->
        <record id="group_medical_insurance_user" model="res.groups">
            <field name="name">User</field>
            <field name="category_id" ref="module_medical_insurance_category"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- Medical Insurance Manager Group -->
        <record id="group_medical_insurance_manager" model="res.groups">
            <field name="name">Manager</field>
            <field name="category_id" ref="module_medical_insurance_category"/>
            <field name="implied_ids" eval="[(4, ref('group_medical_insurance_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <!-- Public Access Group for Employee Personal Information -->
        <record id="group_employee_public_access" model="res.groups">
            <field name="name">Employee Public Access</field>
            <field name="category_id" ref="module_medical_insurance_category"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
            <field name="comment">Allow access to public employee information without HR permissions</field>
        </record>

        <!-- Record Rules -->
        <record id="medical_claim_comp_rule" model="ir.rule">
            <field name="name">Medical Claim: multi-company rule</field>
            <field name="model_id" ref="model_medical_claim"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <record id="medical_insurance_config_comp_rule" model="ir.rule">
            <field name="name">Medical Insurance Config: multi-company rule</field>
            <field name="model_id" ref="model_medical_insurance_config"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <record id="medical_family_member_comp_rule" model="ir.rule">
            <field name="name">Family Member: multi-company rule</field>
            <field name="model_id" ref="model_medical_family_member"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('employee_id.company_id', '=', False), ('employee_id.company_id', 'in', company_ids)]</field>
        </record>
        
        <record id="medical_insurance_fiscal_year_comp_rule" model="ir.rule">
            <field name="name">Medical Insurance Fiscal Year: multi-company rule</field>
            <field name="model_id" ref="model_medical_insurance_fiscal_year"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
        
        <record id="medical_insurance_employee_balance_comp_rule" model="ir.rule">
            <field name="name">Medical Insurance Employee Balance: multi-company rule</field>
            <field name="model_id" ref="model_medical_insurance_employee_balance"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <!-- Employee Portal Rules -->
        <record id="medical_claim_employee_portal_rule" model="ir.rule">
            <field name="name">Medical Claim: employee portal rule</field>
            <field name="model_id" ref="model_medical_claim"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_employee_portal'))]"/>
            <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="medical_family_member_employee_portal_rule" model="ir.rule">
            <field name="name">Family Member: employee portal rule</field>
            <field name="model_id" ref="model_medical_family_member"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_employee_portal'))]"/>
            <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="medical_insurance_fiscal_year_employee_portal_rule" model="ir.rule">
            <field name="name">Medical Insurance Fiscal Year: employee portal rule</field>
            <field name="model_id" ref="model_medical_insurance_fiscal_year"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_employee_portal'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="medical_insurance_employee_balance_employee_portal_rule" model="ir.rule">
            <field name="name">Medical Insurance Employee Balance: employee portal rule</field>
            <field name="model_id" ref="model_medical_insurance_employee_balance"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_employee_portal'))]"/>
            <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="employee_dashboard_employee_portal_rule" model="ir.rule">
            <field name="name">Employee Dashboard: employee portal rule</field>
            <field name="model_id" ref="model_employee_dashboard"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_employee_portal'))]"/>
            <field name="domain_force">[('employee_id.user_id', '=', user.id)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <!-- User and Manager Rules -->
        <record id="medical_claim_user_rule" model="ir.rule">
            <field name="name">Medical Claim: user rule</field>
            <field name="model_id" ref="model_medical_claim"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_user'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="medical_claim_manager_rule" model="ir.rule">
            <field name="name">Medical Claim: manager rule</field>
            <field name="model_id" ref="model_medical_claim"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="medical_family_member_user_rule" model="ir.rule">
            <field name="name">Family Member: user rule</field>
            <field name="model_id" ref="model_medical_family_member"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_user'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="medical_family_member_manager_rule" model="ir.rule">
            <field name="name">Family Member: manager rule</field>
            <field name="model_id" ref="model_medical_family_member"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="medical_insurance_fiscal_year_user_rule" model="ir.rule">
            <field name="name">Medical Insurance Fiscal Year: user rule</field>
            <field name="model_id" ref="model_medical_insurance_fiscal_year"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_user'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="medical_insurance_fiscal_year_manager_rule" model="ir.rule">
            <field name="name">Medical Insurance Fiscal Year: manager rule</field>
            <field name="model_id" ref="model_medical_insurance_fiscal_year"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="medical_insurance_employee_balance_user_rule" model="ir.rule">
            <field name="name">Medical Insurance Employee Balance: user rule</field>
            <field name="model_id" ref="model_medical_insurance_employee_balance"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_user'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="medical_insurance_employee_balance_manager_rule" model="ir.rule">
            <field name="name">Medical Insurance Employee Balance: manager rule</field>
            <field name="model_id" ref="model_medical_insurance_employee_balance"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>

        <record id="employee_dashboard_user_rule" model="ir.rule">
            <field name="name">Employee Dashboard: user rule</field>
            <field name="model_id" ref="model_employee_dashboard"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_user'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="employee_dashboard_manager_rule" model="ir.rule">
            <field name="name">Employee Dashboard: manager rule</field>
            <field name="model_id" ref="model_employee_dashboard"/>
            <field name="groups" eval="[(4, ref('group_medical_insurance_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
    </data>
</odoo>
