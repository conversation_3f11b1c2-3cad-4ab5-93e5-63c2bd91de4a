odoo.define('medical_insurance.medical_dashboard', function (require) {
"use strict";

var FormController = require('web.FormController');
var core = require('web.core');

FormController.include({
    /**
     * @override
     */
    renderButtons: function ($node) {
        this._super.apply(this, arguments);

        // Auto-click the View Mode button when the form is loaded in edit mode
        if (this.modelName === 'employee.dashboard' && this.mode === 'edit') {
            var self = this;
            // Use setTimeout to ensure the form is fully rendered
            setTimeout(function() {
                var $viewModeButton = self.$('.o_form_button_save');
                if ($viewModeButton.length) {
                    $viewModeButton.click();
                }
            }, 500);
        }
    },
});

});
