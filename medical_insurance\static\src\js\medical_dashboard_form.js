odoo.define('medical_insurance.medical_dashboard_form', function (require) {
"use strict";

var FormController = require('web.FormController');
var FormView = require('web.FormView');
var viewRegistry = require('web.view_registry');
var core = require('web.core');

var MedicalDashboardFormController = FormController.extend({
    /**
     * @override
     */
    renderButtons: function ($node) {
        this._super.apply(this, arguments);
        
        // Hide action menu button
        if (this.$buttons) {
            this.$buttons.find('.o_form_button_edit').hide();
            this.$buttons.find('.o_form_button_create').hide();
        }
    },
    
    /**
     * @override
     */
    _updateActionMenus: function () {
        // Prevent action menus from being created
        return;
    },
});

var MedicalDashboardFormView = FormView.extend({
    config: _.extend({}, FormView.prototype.config, {
        Controller: MedicalDashboardFormController,
    }),
});

viewRegistry.add('medical_dashboard_form', MedicalDashboardFormView);

return MedicalDashboardFormView;

});
