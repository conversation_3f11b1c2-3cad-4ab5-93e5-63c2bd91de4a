<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Employee Public Info Form View -->
    <record id="view_employee_public_info_form" model="ir.ui.view">
        <field name="name">employee.public.info.form</field>
        <field name="model">employee.public.info</field>
        <field name="arch" type="xml">
            <form string="Employee Public Information">
                <sheet>
                    <group>
                        <group>
                            <field name="employee_id" readonly="1"/>
                            <field name="employee_name" readonly="1"/>
                            <field name="employee_number" readonly="1"/>
                            <field name="employee_age" readonly="1"/>
                            <field name="employee_marital_status" readonly="1"/>
                        </group>
                        <group>
                            <field name="employee_blood_type" readonly="1"/>
                            <field name="employee_mobile" readonly="1"/>
                            <field name="employee_email" readonly="1"/>
                            <field name="employee_job_title" readonly="1"/>
                            <field name="employee_department" readonly="1"/>
                        </group>
                    </group>
                    <group>
                        <field name="last_updated" readonly="1"/>
                        <field name="company_id" readonly="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Employee Public Info Tree View -->
    <record id="view_employee_public_info_tree" model="ir.ui.view">
        <field name="name">employee.public.info.tree</field>
        <field name="model">employee.public.info</field>
        <field name="arch" type="xml">
            <tree string="Employee Public Information">
                <field name="employee_name"/>
                <field name="employee_number"/>
                <field name="employee_age"/>
                <field name="employee_marital_status"/>
                <field name="employee_blood_type"/>
                <field name="employee_department"/>
                <field name="last_updated"/>
            </tree>
        </field>
    </record>

    <!-- Employee Public Info Search View -->
    <record id="view_employee_public_info_search" model="ir.ui.view">
        <field name="name">employee.public.info.search</field>
        <field name="model">employee.public.info</field>
        <field name="arch" type="xml">
            <search string="Employee Public Information">
                <field name="employee_name"/>
                <field name="employee_number"/>
                <field name="employee_department"/>
                <field name="employee_marital_status"/>
                <field name="employee_blood_type"/>
                
                <filter name="married" string="Married" domain="[('employee_marital_status', 'in', ['Married', 'Married with Dependents'])]"/>
                <filter name="single" string="Single" domain="[('employee_marital_status', '=', 'Single')]"/>
                
                <group expand="0" string="Group By">
                    <filter name="group_by_department" string="Department" domain="[]" context="{'group_by':'employee_department'}"/>
                    <filter name="group_by_marital" string="Marital Status" domain="[]" context="{'group_by':'employee_marital_status'}"/>
                    <filter name="group_by_blood_type" string="Blood Type" domain="[]" context="{'group_by':'employee_blood_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Employee Public Info Action -->
    <record id="action_employee_public_info" model="ir.actions.act_window">
        <field name="name">Employee Public Information</field>
        <field name="res_model">employee.public.info</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Employee public information accessible to all users
            </p>
            <p>
                This view shows employee information that can be accessed without HR permissions.
            </p>
        </field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_employee_public_info"
              name="Employee Public Info"
              parent="menu_medical_insurance_root"
              action="action_employee_public_info"
              sequence="15"/>

    <!-- Server Action to Update All Employee Info -->
    <record id="action_update_all_employee_info" model="ir.actions.server">
        <field name="name">Update All Employee Public Info</field>
        <field name="model_id" ref="model_employee_public_info"/>
        <field name="state">code</field>
        <field name="code">
            updated_count = model.update_all_employees_info()
            action = {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Update Complete',
                    'message': f'Updated {updated_count} employee records',
                    'type': 'success',
                    'sticky': False,
                }
            }
        </field>
    </record>

    <!-- Add Update Action to More Menu -->
    <record id="ir_actions_server_update_employee_info" model="ir.values">
        <field name="name">Update Employee Public Info</field>
        <field name="model">employee.public.info</field>
        <field name="key2">client_action_multi</field>
        <field name="value" eval="'ir.actions.server,' + str(ref('action_update_all_employee_info'))"/>
    </record>
</odoo>
