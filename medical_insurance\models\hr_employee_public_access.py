# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import date


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # حقول محسوبة للعمر والحالة الاجتماعية بدون قيود صلاحيات
    public_age = fields.Integer(
        string='Age',
        compute='_compute_public_age',
        help='Employee age calculated from birth date - accessible to all users'
    )
    
    public_marital_status = fields.Char(
        string='Marital Status',
        compute='_compute_public_marital_status',
        help='Employee marital status - accessible to all users'
    )

    @api.depends('birthday')
    def _compute_public_age(self):
        """Compute employee age from birthday - accessible to all users"""
        for employee in self:
            if employee.birthday:
                today = date.today()
                born = employee.birthday
                employee.public_age = today.year - born.year - ((today.month, today.day) < (born.month, born.day))
            else:
                employee.public_age = 0

    @api.depends('marital')
    def _compute_public_marital_status(self):
        """Compute marital status display - accessible to all users"""
        marital_dict = {
            'single': 'Single',
            'married': 'Married',
            'married_and_provide': 'Married with Dependents',
            'cohabitant': 'Legal Cohabitant',
            'widower': 'Widower',
            'divorced': 'Divorced'
        }
        
        for employee in self:
            employee.public_marital_status = marital_dict.get(employee.marital, employee.marital or '')


class HrEmployeePublic(models.Model):
    _inherit = 'hr.employee.public'

    # إضافة نفس الحقول للنموذج العام
    public_age = fields.Integer(
        string='Age',
        related='employee_id.public_age',
        readonly=True
    )
    
    public_marital_status = fields.Char(
        string='Marital Status',
        related='employee_id.public_marital_status',
        readonly=True
    )
