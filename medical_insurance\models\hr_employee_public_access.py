# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from datetime import date


class HrEmployee(models.Model):
    _inherit = 'hr.employee'

    # حقول محسوبة للعمر والحالة الاجتماعية بدون قيود صلاحيات
    public_age = fields.Integer(
        string='Age',
        compute='_compute_public_age',
        help='Employee age calculated from birth date - accessible to all users'
    )
    
    public_marital_status = fields.Char(
        string='Marital Status',
        compute='_compute_public_marital_status',
        help='Employee marital status - accessible to all users'
    )

    @api.depends('birthday')
    def _compute_public_age(self):
        """Compute employee age from birthday - accessible to all users"""
        for employee in self:
            try:
                # استخدام sudo() للوصول الآمن لحقل birthday
                birthday = employee.sudo().birthday
                if birthday:
                    today = date.today()
                    employee.public_age = today.year - birthday.year - ((today.month, today.day) < (birthday.month, birthday.day))
                else:
                    employee.public_age = 0
            except Exception:
                # في حالة حدوث خطأ، نضع قيمة افتراضية
                employee.public_age = 0

    @api.depends('marital')
    def _compute_public_marital_status(self):
        """Compute marital status display - accessible to all users"""
        marital_dict = {
            'single': 'Single',
            'married': 'Married',
            'married_and_provide': 'Married with Dependents',
            'cohabitant': 'Legal Cohabitant',
            'widower': 'Widower',
            'divorced': 'Divorced'
        }

        for employee in self:
            try:
                # استخدام sudo() للوصول الآمن لحقل marital
                marital_value = employee.sudo().marital
                employee.public_marital_status = marital_dict.get(marital_value, marital_value or '')
            except Exception:
                # في حالة حدوث خطأ، نضع قيمة افتراضية
                employee.public_marital_status = ''


class HrEmployeePublic(models.Model):
    _inherit = 'hr.employee.public'

    # إضافة نفس الحقول للنموذج العام
    public_age = fields.Integer(
        string='Age',
        related='employee_id.public_age',
        readonly=True
    )
    
    public_marital_status = fields.Char(
        string='Marital Status',
        related='employee_id.public_marital_status',
        readonly=True
    )
